// Fill out your copyright notice in the Description page of Project Settings.


#include "VictorPlayerController.h"
#include "Victor/UserInterface/PlayerUiWidget.h"

void AVictorPlayerController::BeginPlay()
{
	Super::BeginPlay();

	PlayerUiWidget = Cast<UPlayerUiWidget>( CreateWidget( this, PlayerUiClass ) );

	if( PlayerUiWidget != nullptr )
	{
		PlayerUiWidget->AddToViewport();
		PlayerUiWidget->PushDebugUiStack();
	}
}