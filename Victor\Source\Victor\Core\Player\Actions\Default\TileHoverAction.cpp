// Fill out your copyright notice in the Description page of Project Settings.


#include "TileHoverAction.h"
#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"
#include "Victor/Core/Grid/Utilities/TileStateEnum.h"

void UTileHoverAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();

		if (HoveredIndex != Index)
		{
			CurrentGrid->RemoveTileState(HoveredIndex, ETileStateEnum::Hovered);

			HoveredIndex = Index;
			CurrentGrid->AddTileState(HoveredIndex, ETileStateEnum::Hovered);
		}
	}
}

bool UTileHoverAction::CanExecute(UWorld* World) const
{
	return true;
}

FString UTileHoverAction::GetActionName() const
{
	return TEXT("SelectTileAction");
}
