// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "PlayerAction.generated.h"

// This class does not need to be modified.
UINTERFACE(MinimalAPI)
class UPlayerAction : public UInterface
{
	GENERATED_BODY()
};

/**
 * 
 */
class VICTOR_API IPlayerAction
{
	GENERATED_BODY()

public:
	virtual void Execute(UWorld* World) = 0;
	virtual bool CanExecute(UWorld* World) const { return true; }
	virtual FString GetActionName() const = 0;
};
