// Fill out your copyright notice in the Description page of Project Settings.


#include "VictorPlayerPawn.h"
#include "VictorPlayerController.h"
#include "Math/UnrealMathUtility.h"
#include "EnhancedInputSubsystems.h"
#include "EnhancedInputComponent.h"
#include "PlayerActionsComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Camera/CameraComponent.h"

// Sets default values
AVictorPlayerPawn::AVictorPlayerPawn()
{
	// Set this pawn to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	SpringArm = CreateDefaultSubobject<USpringArmComponent>(TEXT("SpringArm"));
	SpringArm->SetupAttachment(RootComponent);

	Camera = CreateDefaultSubobject<UCameraComponent>(TEXT("Camera"));
	Camera->SetupAttachment(SpringArm);

	PlayerActionsComponent = CreateDefaultSubobject<UPlayerActionsComponent>(TEXT("PlayerActionsComponent"));
}

// Called when the game starts or when spawned
void AVictorPlayerPawn::BeginPlay()
{
	Super::BeginPlay();

	VictorPlayerController = Cast<AVictorPlayerController>(GetController());
	if (VictorPlayerController)
	{
		VictorPlayerController->bShowMouseCursor = true;

		Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(
			VictorPlayerController->GetLocalPlayer());

		if (Subsystem)
		{
			Subsystem->AddMappingContext(DefaultPlayerContext, 0);
		}
	}

	DesiredZoom = SpringArm->TargetArmLength;
	DesiredMovement = GetActorLocation();
	DesiredRotation = GetActorRotation();
}

// Called every frame
void AVictorPlayerPawn::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	float CurrentArmLength = SpringArm->TargetArmLength;
	SpringArm->TargetArmLength = FMath::FInterpTo(CurrentArmLength, DesiredZoom, DeltaTime, ZoomInterpSpeed);

	FVector CameraLocation = GetActorLocation();
	FVector NewLocation = FMath::VInterpTo(CameraLocation, DesiredMovement, DeltaTime, MovementInterpSpeed);
	SetActorLocation(NewLocation);

	FRotator ActorRotation = GetActorRotation();
	FRotator NewRotation = FMath::RInterpTo(ActorRotation, DesiredRotation, DeltaTime, RotateInterpSpeed);
	SetActorRotation(NewRotation);
}

#pragma region Input Functions

// Called to bind functionality to input
void AVictorPlayerPawn::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	if (UEnhancedInputComponent* EnhancedInputComponent = CastChecked<UEnhancedInputComponent>(PlayerInputComponent))
	{
		EnhancedInputComponent->BindAction(ZoomAction, ETriggerEvent::Triggered, this, &AVictorPlayerPawn::ZoomInput);
		EnhancedInputComponent->BindAction(BattleCamAction, ETriggerEvent::Triggered, this, &AVictorPlayerPawn::BattleCamInput);
		EnhancedInputComponent->BindAction(RotateAction, ETriggerEvent::Triggered, this, &AVictorPlayerPawn::RotateInput);
		EnhancedInputComponent->BindAction(PrimaryAction, ETriggerEvent::Triggered, this, &AVictorPlayerPawn::PrimaryInput);
		EnhancedInputComponent->BindAction(SecondaryAction, ETriggerEvent::Triggered, this, &AVictorPlayerPawn::SecondaryInput);
	}
}

void AVictorPlayerPawn::ZoomInput(const FInputActionValue& Value)
{
	const float ZoomValue = Value.Get<float>();

	float ZoomRate = ZoomValue * ZoomMultiplier;
	float NewLength = DesiredZoom + ZoomRate;
	DesiredZoom = FMath::Clamp(NewLength, MinZoomValue, MaxZoomValue);
}

void AVictorPlayerPawn::BattleCamInput(const FInputActionValue& Value)
{
	const FVector2D DirectionalValue = Value.Get<FVector2D>();

	// Get the camera's forward and right vectors
	FVector Forward = Camera->GetForwardVector();
	FVector Right = Camera->GetRightVector();

	// Flatten the vectors to the X-Y plane (remove vertical Z component)
	Forward.Z = 0.f;
	Right.Z = 0.f;
	Forward.Normalize();
	Right.Normalize();

	// Apply input
	FVector MoveDirection = (Forward * DirectionalValue.Y + Right * DirectionalValue.X) * MovementMultiplier;
	DesiredMovement += MoveDirection;
}

void AVictorPlayerPawn::RotateInput(const FInputActionValue& Value)
{
	//UE_LOG( LogTemp, Display, TEXT( "Here is my Angle: %f" ), RotationAngle );
	const float RotateValue = Value.Get<float>();
	DesiredRotation += RotationAngle * RotateValue;
}

void AVictorPlayerPawn::PrimaryInput(const FInputActionValue& Value)
{
	PlayerActionsComponent->PrimaryAction();
}

void AVictorPlayerPawn::SecondaryInput(const FInputActionValue& Value)
{
	PlayerActionsComponent->SecondaryAction();
}

#pragma endregion

#pragma region Getters and Setters
// Zoom Values
float AVictorPlayerPawn::GetZoomMultiplier() const
{
	return ZoomMultiplier;
}

void AVictorPlayerPawn::SetZoomMultiplier(float Value)
{
	ZoomMultiplier = Value;
}

float AVictorPlayerPawn::GetMinZoomValue() const
{
	return MinZoomValue;
}

void AVictorPlayerPawn::SetMinZoomValue(float Value)
{
	MinZoomValue = Value;
}

float AVictorPlayerPawn::GetMaxZoomValue() const
{
	return MaxZoomValue;
}

void AVictorPlayerPawn::SetMaxZoomValue(float Value)
{
	MaxZoomValue = Value;
}

float AVictorPlayerPawn::GetZoomInterpSpeed() const
{
	return ZoomInterpSpeed;
}

void AVictorPlayerPawn::SetZoomInterpSpeed(float Value)
{
	ZoomInterpSpeed = Value;
}

// Movement Values
float AVictorPlayerPawn::GetMovementMultiplier() const
{
	return MovementMultiplier;
}

void AVictorPlayerPawn::SetMovementMultiplier(float Value)
{
	MovementMultiplier = Value;
}

float AVictorPlayerPawn::GetMovementInterpSpeed() const
{
	return MovementInterpSpeed;
}

void AVictorPlayerPawn::SetMovementInterpSpeed(float Value)
{
	MovementInterpSpeed = Value;
}

// Rotate Values
float AVictorPlayerPawn::GetRotationAngle() const
{
	return RotationAngle.Yaw;
}

void AVictorPlayerPawn::SetRotationAngle(const float& Value)
{
	RotationAngle.Yaw = Value;
}

float AVictorPlayerPawn::GetRotateInterpSpeed() const
{
	return RotateInterpSpeed;
}

void AVictorPlayerPawn::SetRotateInterpSpeed(float Value)
{
	RotateInterpSpeed = Value;
}
#pragma endregion
