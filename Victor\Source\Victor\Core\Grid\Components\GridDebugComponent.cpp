// Fill out your copyright notice in the Description page of Project Settings.

#include "GridDebugComponent.h"
#include "Engine/World.h"
#include "Engine/TextRenderActor.h"
#include "Components/TextRenderComponent.h"
#include "Victor/Core/Grid/Utilities/GridFunctionLibrary.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"

// Sets default values for this component's properties
UGridDebugComponent::UGridDebugComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = false;

	// ...
}


// Called when the game starts
void UGridDebugComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
}

void UGridDebugComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	ClearTileTextActorMap();
	Super::EndPlay(EndPlayReason);
}

void UGridDebugComponent::Begin<PERSON><PERSON>roy()
{
	ClearTileTextActorMap();
	Super::BeginDestroy();
}

void UGridDebugComponent::SetDebugEnabled(const bool bIsEnabled)
{
	bDebugEnabled = bIsEnabled;
}

bool UGridDebugComponent::GetDebugEnabled() const
{
	return bDebugEnabled;
}

// Called every frame
void UGridDebugComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// ...
}

ATextRenderActor* UGridDebugComponent::GetTileTextActor(const FIntPoint Index)
{
	ATextRenderActor* TileText = TileTextMap.FindRef(Index);

	if (TileText)
	{
		return TileText;
	}

	if (UWorld* World = GetWorld())
	{
		const FVector SpawnLocation = FVector(0.0f, 0.0f, 100.0f);
		const FRotator SpawnRotation = FRotator::ZeroRotator;

		TileText = World->SpawnActor<ATextRenderActor>(SpawnLocation, SpawnRotation);

		TileTextMap.Add(Index, TileText);

		TileText->SetActorTickEnabled(false);
		TileText->SetActorEnableCollision(false);

		TileText->GetTextRender()->SetHorizontalAlignment(EHTA_Center);
		TileText->GetTextRender()->SetVerticalAlignment(EVRTA_TextCenter);
	}

	return TileText;
}

void UGridDebugComponent::DestroyTileTextActor(const FIntPoint Index)
{
	if (ATextRenderActor* TileText = TileTextMap.FindRef(Index))
	{
		if (UWorld* World = GetWorld())
		{
			World->DestroyActor(TileText);
			TileTextMap.Remove(Index);
		}
	}
}

void UGridDebugComponent::ClearTileTextActorMap()
{
	TArray<FIntPoint> IndexArray;
	TileTextMap.GetKeys(IndexArray);

	for (const FIntPoint Index : IndexArray)
	{
		DestroyTileTextActor(Index);
	}
}

void UGridDebugComponent::UpdateTileText(const FIntPoint Index, const FTileDataStruct* TileData)
{
	if (bDebugEnabled)
	{
		const bool bShouldShowText = TileData != nullptr && UGridFunctionLibrary::IsTileWalkable(TileData->TileType);

		if (bShouldShowText)
		{
			ATextRenderActor* TileText = GetTileTextActor(Index);
			const FText TileInfo = UGridFunctionLibrary::GetIndexText(Index);
			TileText->GetTextRender()->SetText(TileInfo);

			const FTransform TileTransform = TileData->Transform;
			const FVector TileLocation = TileTransform.GetLocation();
			const FVector TextLocation = FVector(TileLocation.X, TileLocation.Y, TileLocation.Z + 1);

			const FRotator TextRotation = FRotator(90, 180, 0);
			const FVector TextScale = FVector(2, 2, 2);

			TileText->SetActorTransform(FTransform(TextRotation, TextLocation, TextScale));
		}
		else
		{
			DestroyTileTextActor(Index);
		}
	}
}
