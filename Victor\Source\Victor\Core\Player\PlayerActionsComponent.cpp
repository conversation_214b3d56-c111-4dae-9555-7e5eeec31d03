// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerActionsComponent.h"

#include "Actions/PlayerAction.h"
#include "Subsystems/PlayerActionContextSubsystem.h"

// Sets default values for this component's properties
UPlayerActionsComponent::UPlayerActionsComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	// ...
}


// Called when the game starts
void UPlayerActionsComponent::BeginPlay()
{
	Super::BeginPlay();

	if (const APawn* OwningPawn = Cast<APawn>(GetOwner()))
	{
		if (const APlayerController* PlayerController = Cast<APlayerController>(OwningPawn->GetController()))
		{
			if (ULocalPlayer* LocalPlayer = PlayerController->GetLocalPlayer())
			{
				ContextManager = LocalPlayer->GetSubsystem<UPlayerActionContextSubsystem>();
			}
		}
	}
}

void UPlayerActionsComponent::UpdateCursorTile() const
{
	if (ContextManager)
	{
		if (UObject* ActionObj = ContextManager->GetActionForInput(EPlayerInputAction::Continuous))
		{
			if (IPlayerAction* Action = Cast<IPlayerAction>(ActionObj))
			{
				UWorld* World = GetWorld();
				if (Action->CanExecute(World))
				{
					Action->Execute(World);
				}
			}
		}
	}
}

void UPlayerActionsComponent::PrimaryAction() const
{
	if (ContextManager)
	{
		if (UObject* ActionObj = ContextManager->GetActionForInput(EPlayerInputAction::Primary))
		{
			if (IPlayerAction* Action = Cast<IPlayerAction>(ActionObj))
			{
				UWorld* World = GetWorld();
				if (Action->CanExecute(World))
				{
					Action->Execute(World);
				}
			}
		}
	}
}

void UPlayerActionsComponent::SecondaryAction() const
{
	if (ContextManager)
	{
		if (UObject* ActionObj = ContextManager->GetActionForInput(EPlayerInputAction::Secondary))
		{
			if (IPlayerAction* Action = Cast<IPlayerAction>(ActionObj))
			{
				UWorld* World = GetWorld();
				if (Action->CanExecute(World))
				{
					Action->Execute(World);
				}
			}
		}
	}
}

// Called every frame
void UPlayerActionsComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	UpdateCursorTile();
}
