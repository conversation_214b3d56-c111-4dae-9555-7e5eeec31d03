// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "PathfindingActivatableWidget.generated.h"

class AGrid;
class UCheckBox;
/**
 * 
 */
UCLASS()
class VICTOR_API UPathfindingActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
protected:
	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCheckBox* ShowTileTextCheckBox;

private:
	AGrid* CurrentGrid;

	//Functions
	UFUNCTION()
	void OnShowTileTextValueChange(bool bIsChecked);

public:
	virtual void NativeOnInitialized() override;

	virtual void NativeConstruct() override;
};
