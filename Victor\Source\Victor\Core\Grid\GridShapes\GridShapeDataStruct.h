#pragma once

#include "CoreMinimal.h"
#include "GridShapeDataStruct.generated.h"

USTRUCT( BlueprintType )
struct VICTOR_API FGridShapeDataStruct : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY( EditDefaultsOnly, BlueprintReadOnly )
	FVector MeshSize = FVector( 100.0f, 100.0f, 100.0f );

	UPROPERTY( EditDefaultsOnly, BlueprintReadOnly )
	TSoftObjectPtr<UStaticMesh> Mesh;

	UPROPERTY( EditDefaultsOnly, BlueprintReadOnly )
	TSoftObjectPtr<UMaterialInstance> MeshMaterial;

	UPROPERTY( EditDefaultsOnly, BlueprintReadOnly )
	TSoftObjectPtr<UStaticMesh> FlatMesh;

	UPROPERTY( EditDefaultsOnly, BlueprintReadOnly )
	TSoftObjectPtr<UMaterialInstance> FlatMaterial;
};
