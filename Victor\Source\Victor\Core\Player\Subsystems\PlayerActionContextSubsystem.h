// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/LocalPlayerSubsystem.h"
#include "Victor/Core/Player/Types/PlayerActionTypes.h"
#include "PlayerActionContextSubsystem.generated.h"

class IPlayerAction;

USTRUCT()
struct FActionMap
{
	GENERATED_BODY()
	UPROPERTY()
	TMap<EPlayerInputAction, TObjectPtr<UObject>> Actions;
};

/**
 * 
 */
UCLASS()
class VICTOR_API UPlayerActionContextSubsystem : public ULocalPlayerSubsystem
{
	GENERATED_BODY()

	UPROPERTY()
	TMap<EPlayerActionContext, FActionMap> ContextActions;

	EPlayerActionContext CurrentContext = EPlayerActionContext::Default;

public:
	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	UFUNCTION(BlueprintCallable)
	void SetContext(EPlayerActionContext NewContext);

	UFUNCTION(BlueprintCallable)
	void RegisterAction(EPlayerActionContext Context, EPlayerInputAction InputAction, UObject* Action);

	UFUNCTION(BlueprintCallable)
	UObject* GetActionForInput(EPlayerInputAction InputAction) const;

	UFUNCTION(BlueprintCallable)
	EPlayerActionContext GetCurrentContext() const;

private:
	void SetupDefaultActions();
};
