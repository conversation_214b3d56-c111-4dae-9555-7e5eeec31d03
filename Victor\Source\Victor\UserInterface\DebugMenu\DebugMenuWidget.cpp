// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugMenuWidget.h"
#include "CommonButtonBase.h"
#include "CommonActivatableWidgetSwitcher.h"

void UDebugMenuWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if (CommandsTabButton)
	{
		CommandsTabButton->OnClicked().AddUObject(this, &UDebugMenuWidget::OnCommandsTabButtonClick);
		TabButtonArray.Add(CommandsTabButton);
	}

	if (CameraTabButton)
	{
		CameraTabButton->OnClicked().AddUObject(this, &UDebugMenuWidget::OnCameraBtnClick);
		TabButtonArray.Add(CameraTabButton);
	}

	if (GridTabButton)
	{
		GridTabButton->OnClicked().AddUObject(this, &UDebugMenuWidget::OnGridTabButtonClick);
		TabButtonArray.Add(GridTabButton);
	}

	if (PathfindingTabButton)
	{
		PathfindingTabButton->OnClicked().AddUObject(this, &UDebugMenuWidget::OnPathfindingTabButtonClick);
		TabButtonArray.Add(PathfindingTabButton);
	}
}

void UDebugMenuWidget::OnCommandsTabButtonClick()
{
	OnTabButtonClick(1, CommandsTabButton);
}

void UDebugMenuWidget::OnCameraBtnClick()
{
	OnTabButtonClick(2, CameraTabButton);
}

void UDebugMenuWidget::OnGridTabButtonClick()
{
	OnTabButtonClick(3, GridTabButton);
}

void UDebugMenuWidget::OnPathfindingTabButtonClick()
{
	OnTabButtonClick(4, PathfindingTabButton);
}

void UDebugMenuWidget::OnTabButtonClick(const int32 WidgetIndex, const UCommonButtonBase* SelectedButton)
{
	for (UCommonButtonBase* Button : TabButtonArray)
	{
		if (Button != SelectedButton)
		{
			Button->SetIsSelected(false);
		}
	}

	const int32 CurrentIndex = DebugSwitcher->GetActiveWidgetIndex();
	if (CurrentIndex == WidgetIndex)
	{
		DebugSwitcher->SetActiveWidgetIndex(0);
	}
	else
	{
		DebugSwitcher->SetActiveWidgetIndex(WidgetIndex);
	}
}
