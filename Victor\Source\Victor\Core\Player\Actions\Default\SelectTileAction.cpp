// Fill out your copyright notice in the Description page of Project Settings.


#include "SelectTileAction.h"

#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"
#include "Victor/Core/Grid/Utilities/TileStateEnum.h"

void USelectTileAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();

		if (Index != SelectedIndex)
		{
			CurrentGrid->RemoveTileState(SelectedIndex, ETileStateEnum::Selected);
			SelectedIndex = Index;
			CurrentGrid->AddTileState(Index, ETileStateEnum::Selected);
		}
		else
		{
			SelectedIndex = NULL;
			CurrentGrid->RemoveTileState(Index, ETileStateEnum::Selected);
		}
	}
}

bool USelectTileAction::CanExecute(UWorld* World) const
{
	return true;
}

FString USelectTileAction::GetActionName() const
{
	return TEXT("SelectTileAction");
}
