// Fill out your copyright notice in the Description page of Project Settings.


#include "GridFunctionLibrary.h"
#include "TileTypeEnum.h"

bool UGridFunctionLibrary::IsTileWalkable(const ETileTypeEnum TileType)
{
	if (TileType == ETileTypeEnum::None || TileType == ETileTypeEnum::Obstacle)
	{
		return false;
	}
	return true;
}

FVector UGridFunctionLibrary::SnapVectorToVector(FVector Vector1, FVector Vector2)
{
	float AlignedX = FMath::GridSnap(Vector1.X, Vector2.X);
	float AlignedY = FMath::GridSnap(Vector1.Y, Vector2.Y);
	float AlignedZ = FMath::GridSnap(Vector1.Z, Vector2.Z);

	FVector AlignedVector = FVector(AlignedX, AlignedY, AlignedZ);
	return AlignedVector;
}

bool UGridFunctionLibrary::IsFloatEven(float LocalFloat)
{
	int64 IntValue = FMath::RoundToFloat(LocalFloat);
	float ModFloat = IntValue % 2;

	bool IsEven;
	if (ModFloat == 0)
	{
		IsEven = true;
	}
	else
	{
		IsEven = false;
	}

	return IsEven;
}

FText UGridFunctionLibrary::GetIndexText(const FIntPoint Index)
{
	FText PointText = FText::Format(
		FText::FromString(TEXT("{0},{1}")),
		FText::AsNumber(Index.X),
		FText::AsNumber(Index.Y));

	return PointText;
}
