// Fill out your copyright notice in the Description page of Project Settings.


#include "CameraTabActivatableWidget.h"
#include "Victor/UserInterface/DebugMenu/Components/Sliders/DebugSliderActivatableWidget.h"
#include "Victor/Core/Player/VictorPlayerPawn.h"
#include "Kismet/GameplayStatics.h"

void UCameraTabActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( MovementSpeedSpinBox )
	{
		MovementSpeedSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnMovementSpeedSpinBoxValueChange );
	}

	if( MovementInterpSpinBox )
	{
		MovementInterpSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnMovementInterpSpinBoxValueChange );
	}

	if( RotationAngleSpinBox )
	{
		RotationAngleSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnRotationAngleSpinBoxValueChange );
	}

	if( RotationInterpSpinBox )
	{
		RotationInterpSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnRotationInterpSpinBoxValueChange );
	}

	if( ZoomSpeedSpinBox )
	{
		ZoomSpeedSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnZoomSpeedSpinBoxValueChange );
	}

	if( ZoomInterpSpinBox )
	{
		ZoomInterpSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnZoomInterpSpinBoxValueChange );
	}

	if( ZoomMinSpinBox )
	{
		ZoomMinSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnZoomMinSpinBoxValueChange );
	}

	if( ZoomMaxSpinBox )
	{
		ZoomMaxSpinBox->ValueChangeDelegate.BindUObject( this, &UCameraTabActivatableWidget::OnZoomMaxSpinBoxValueChange );
	}
}

void UCameraTabActivatableWidget::NativeConstruct()
{
	Super::NativeConstruct();

	UWorld* World = GetWorld();
	APawn* PlayerPawn = UGameplayStatics::GetPlayerPawn( World, 0 );
	if( PlayerPawn )
	{
		VictorPlayerPawn = Cast< AVictorPlayerPawn>( PlayerPawn );

		if( VictorPlayerPawn )
		{
			MovementSpeedSpinBox->SetValue( VictorPlayerPawn->GetMovementMultiplier() );
			MovementInterpSpinBox->SetValue( VictorPlayerPawn->GetMovementInterpSpeed() );

			RotationAngleSpinBox->SetValue( VictorPlayerPawn->GetRotationAngle() );
			RotationInterpSpinBox->SetValue( VictorPlayerPawn->GetRotateInterpSpeed() );

			ZoomSpeedSpinBox->SetValue( VictorPlayerPawn->GetZoomMultiplier() );
			ZoomInterpSpinBox->SetValue( VictorPlayerPawn->GetZoomInterpSpeed() );

			ZoomMinSpinBox->SetValue( VictorPlayerPawn->GetMinZoomValue() );
			ZoomMaxSpinBox->SetValue( VictorPlayerPawn->GetMaxZoomValue() );
		}
	}
}

void UCameraTabActivatableWidget::OnMovementSpeedSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetMovementMultiplier( NewValue );
	}
}

void UCameraTabActivatableWidget::OnMovementInterpSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetMovementInterpSpeed( NewValue );
	}
}

void UCameraTabActivatableWidget::OnRotationAngleSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetRotationAngle( NewValue );
	}
}

void UCameraTabActivatableWidget::OnRotationInterpSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetRotateInterpSpeed( NewValue );
	}
}

void UCameraTabActivatableWidget::OnZoomSpeedSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetZoomMultiplier( NewValue );
	}
}

void UCameraTabActivatableWidget::OnZoomInterpSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetZoomInterpSpeed( NewValue );
	}
}

void UCameraTabActivatableWidget::OnZoomMinSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetMinZoomValue( NewValue );
	}
}

void UCameraTabActivatableWidget::OnZoomMaxSpinBoxValueChange( float NewValue )
{
	if( VictorPlayerPawn )
	{
		VictorPlayerPawn->SetMaxZoomValue( NewValue );
	}
}