// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerUiWidget.h"
#include "Widgets/CommonActivatableWidgetContainer.h"
#include "CommonActivatableWidget.h"

void UPlayerUiWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

}

UCommonActivatableWidget* UPlayerUiWidget::PushDebugUiStack()
{
	UCommonActivatableWidget* Widget = DebugMenuStack->AddWidget( DebugMenuUiClass );
	return Widget;
}
