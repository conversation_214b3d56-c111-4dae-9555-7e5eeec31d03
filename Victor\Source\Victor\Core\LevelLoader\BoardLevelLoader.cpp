// Fill out your copyright notice in the Description page of Project Settings.


#include "BoardLevelLoader.h"
#include "Kismet/GameplayStatics.h" // For UGameplayStatics
#include "Engine/World.h" // For UWorld

void UBoardLevelLoader::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	if (BoardLevels.Num() > 0)
	{
		CurrentLevelIndex = 0;
		PreviousLevelIndex = CurrentLevelIndex;
		LoadCurrentLevel();
	}
}

void UBoardLevelLoader::Deinitialize()
{
	Super::Deinitialize();
}

TArray<FString> UBoardLevelLoader::GetGameLevelNamesForDropdown() const
{
	TArray<FString> LevelNames;
	LevelNames.Reserve(BoardLevels.Num()); // Pre-allocate memory for efficiency

	for (const TSoftObjectPtr<UWorld>& LevelPtr : BoardLevels)
	{
		if (!LevelPtr.IsNull()) // Check if the reference is valid (not empty)
		{
			LevelNames.Add(LevelPtr.GetAssetName());
		}
		else
		{
			LevelNames.Add(TEXT("Invalid Level Reference")); // Or an empty string
		}
	}
	return LevelNames;
}

void UBoardLevelLoader::UnloadPrevLevel()
{
	if (BoardLevels.IsValidIndex(PreviousLevelIndex))
	{
		TSoftObjectPtr<UWorld> PrevLevel = BoardLevels[PreviousLevelIndex];
		if (!PrevLevel.IsNull())
		{
			FLatentActionInfo LatentInfo;
			LatentInfo.CallbackTarget = this;
			LatentInfo.ExecutionFunction = TEXT("OnLevelUnload"); // Function to call when loading is done
			LatentInfo.UUID = GetUniqueID();
			LatentInfo.Linkage = 0;

			UGameplayStatics::UnloadStreamLevelBySoftObjectPtr(GetWorld(), PrevLevel, LatentInfo, false);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT( "Attempted to unload an invalid level reference at index: %d" ), PreviousLevelIndex);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT( "Invalid prev level index provided: %d" ), PreviousLevelIndex);
	}
}

void UBoardLevelLoader::LoadCurrentLevel()
{
	if (BoardLevels.IsValidIndex(CurrentLevelIndex))
	{
		TSoftObjectPtr<UWorld> LevelToLoad = BoardLevels[CurrentLevelIndex];
		if (!LevelToLoad.IsNull())
		{
			FLatentActionInfo LatentInfo;
			LatentInfo.CallbackTarget = this;
			LatentInfo.ExecutionFunction = TEXT("OnLevelStreamed"); // Function to call when loading is done
			LatentInfo.UUID = GetUniqueID();
			LatentInfo.Linkage = 0;


			UGameplayStatics::LoadStreamLevelBySoftObjectPtr(
				GetWorld(),
				LevelToLoad,
				true, // bMakeVisibleAfterLoad
				false, // bShouldBlockOnLoad (set to false for true async)
				LatentInfo
			);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT( "Attempted to load an invalid level reference at index: %d" ), CurrentLevelIndex);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT( "Invalid level index provided: %d" ), CurrentLevelIndex);
	}
}

void UBoardLevelLoader::LoadLevelByIndex(int32 LevelIndex)
{
	CurrentLevelIndex = LevelIndex;
	UnloadPrevLevel();
}

void UBoardLevelLoader::OnLevelStreamed()
{
	UE_LOG(LogTemp, Log, TEXT( "Level has been streamed" ));
}

void UBoardLevelLoader::OnLevelUnload()
{
	PreviousLevelIndex = CurrentLevelIndex;
	LoadCurrentLevel();
}
