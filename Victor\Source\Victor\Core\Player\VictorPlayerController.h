// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "VictorPlayerController.generated.h"

class UPlayerUiWidget;

/**
 *
 */
UCLASS()
class VICTOR_API AVictorPlayerController : public APlayerController
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( EditAnywhere )
	TSubclassOf<UPlayerUiWidget> PlayerUiClass;

protected:
	UPROPERTY( BlueprintReadOnly )
	UPlayerUiWidget* PlayerUiWidget;

	//Functions
public:
	void BeginPlay() override;
};
