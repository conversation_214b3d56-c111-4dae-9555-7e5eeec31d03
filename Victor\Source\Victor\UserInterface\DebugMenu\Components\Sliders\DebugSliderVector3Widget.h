// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "DebugSliderVector3Widget.generated.h"

class UCommonTextBlock;
class USpinBox;

DECLARE_DELEGATE_OneParam( FSliderVector3ValueChangeDelegate, FVector );

/**
 *
 */
UCLASS()
class VICTOR_API UDebugSliderVector3Widget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FText SliderName;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxSliderValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector Value;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxFractDigits;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	bool AlwaysSnapToDelta;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	float Delta;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonTextBlock* DisplayName;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugXValueSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugYValueSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugZValueSpinBox;

public:
	FSliderVector3ValueChangeDelegate ValueChangeDelegate;

	//Functions
private:
	UFUNCTION()
	void OnSliderXValueChange( float NewValue );

	UFUNCTION()
	void OnSliderYValueChange( float NewValue );

	UFUNCTION()
	void OnSliderZValueChange( float NewValue );

public:
	void NativePreConstruct() override;

	void NativeOnInitialized() override;

	FVector GetValue() const;
	void SetValue( FVector NewValue );

};
