// Fill out your copyright notice in the Description page of Project Settings.


#include "Grid.h"
#include "Victor/Core/Grid/GridShapes/GridShapeDataStruct.h"
#include "Math/UnrealMathUtility.h"
#include "Engine/World.h"
#include "CollisionShape.h"
#include "GridModifier.h"
#include "Components/GridDebugComponent.h"
#include "Components/GridPathfindingComponent.h"
#include "Components/GridVisualComponent.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Kismet/GameplayStatics.h"
#include "Utilities/GridFunctionLibrary.h"

// Sets default values
AGrid::AGrid()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	GridRootScene = CreateDefaultSubobject<USceneComponent>(TEXT("GridRootScene"));
	SetRootComponent(GridRootScene);

	GridVisualComponent = CreateDefaultSubobject<UGridVisualComponent>(TEXT("GridVisualComponent"));
	GridDebugComponent = CreateDefaultSubobject<UGridDebugComponent>(TEXT("GridDebugComponent"));
	GridPathfindingComponent = CreateDefaultSubobject<UGridPathfindingComponent>(TEXT("GridPathfindingComponent"));
}

void AGrid::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);

	if (GridDebugComponent)
	{
		GridDebugComponent->SetDebugEnabled(false);
	}

	CenterLocation = GetActorLocation();
	SpawnGrid(true);
}

// Called when the game starts or when spawned
void AGrid::BeginPlay()
{
	Super::BeginPlay();

	GridVisualComponent->SetGroundOffset(GroundHeightOffset);
	GridVisualComponent->SetInstanceMeshCollision(GridCollisionEnabled, GridCollisionResponse);
}

// Called every frame
void AGrid::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

const FGridShapeDataStruct* AGrid::SetStaticMeshValues()
{
	// Use the stored data table pointer
	const FGridShapeDataStruct* GridShapeData = GetGridShapeData();

	if (GridShapeData)
	{
		const TSoftObjectPtr<UStaticMesh> SoftShapeMesh = GridShapeData->FlatMesh;
		const UStaticMesh* ShapeMesh = SoftShapeMesh.LoadSynchronous();

		const TSoftObjectPtr<UMaterialInstance> SoftFlatMaterial = GridShapeData->FlatMaterial;
		const UMaterialInstance* FlatMaterial = SoftFlatMaterial.LoadSynchronous();

		if (ShapeMesh && FlatMaterial)
		{
			GridVisualComponent->InitializeGridVisual(this);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT( "No Row" ));
	}

	return GridShapeData;
}

FVector2D AGrid::GetAlignedTileCount() const
{
	const float IsTileCountXEven = UGridFunctionLibrary::IsFloatEven(TileCount.X);
	const float IsTileCountYEven = UGridFunctionLibrary::IsFloatEven(TileCount.Y);

	float XSubtractor;
	if (IsTileCountXEven)
	{
		XSubtractor = 0;
	}
	else
	{
		XSubtractor = 1;
	}

	float YSubtractor;
	if (IsTileCountYEven)
	{
		YSubtractor = 0;
	}
	else
	{
		YSubtractor = 1;
	}

	const FVector2D AlignedTileCount = FVector2D(TileCount.X - XSubtractor, TileCount.Y - YSubtractor);
	return AlignedTileCount;
}

FVector AGrid::FindGridBottomLeftLocation()
{
	FVector CalculatedBottomLeft;
	FVector AlignedCenter = FVector(0, 0, 0);

	if (Shape == EGridShapeEnum::Square)
	{
		AlignedCenter = UGridFunctionLibrary::SnapVectorToVector(CenterLocation, TileSize);

		const FVector2D AlignedTileCount = GetAlignedTileCount();
		const FVector2D HalfGridCount = AlignedTileCount / 2;
		const FVector2D HalfGrid = FVector2D(TileSize.X, TileSize.Y) * HalfGridCount;
		CalculatedBottomLeft = AlignedCenter - FVector(HalfGrid.X, HalfGrid.Y, 0.0f);
	}
	else if (Shape == EGridShapeEnum::Hexagon)
	{
		const FVector HexSizeAlignment = TileSize * FVector(1.5, 1, 1);
		AlignedCenter = UGridFunctionLibrary::SnapVectorToVector(CenterLocation, HexSizeAlignment);

		const FVector2D HalfGridCount = TileCount / FIntPoint(3, 2);
		const FVector2D HalfGrid = FVector2D(TileSize.X, TileSize.Y) * HalfGridCount;
		const FVector AlignedHalfGrid = UGridFunctionLibrary::SnapVectorToVector(FVector(HalfGrid.X, HalfGrid.Y, 0.0f), HexSizeAlignment);
		CalculatedBottomLeft = AlignedCenter - AlignedHalfGrid - (TileSize * FVector(1.5, 0.5, 1));
	}
	else if (Shape == EGridShapeEnum::Triangle)
	{
		//Triangle is thinner on the x axies and thus needs to snap more on the x axis
		//Thus instead of using the normal tile size on when to snap, we instead double the x
		const FVector TriangleSizeAlignment = TileSize * FVector(2, 1, 1);
		AlignedCenter = UGridFunctionLibrary::SnapVectorToVector(CenterLocation, TriangleSizeAlignment);

		//The GetAlignedTileCount really only works for squares since the squares line up perfectly
		//This means for triangles we have to simply align on -1,-1 instead
		const FVector2D AlignedTileCount = FVector2D(TileCount.X - 1, TileCount.Y - 1);

		//Where before we only got half, triangles have to do half for x but 1/4 for Y
		const FVector2D HalfGridCount = AlignedTileCount / FVector2D(2, 4);
		const FVector2D HalfGrid = FVector2D(TileSize.X, TileSize.Y) * HalfGridCount;
		const FVector AlignedHalfGrid = UGridFunctionLibrary::SnapVectorToVector(FVector(HalfGrid.X, HalfGrid.Y, 0.0f), TriangleSizeAlignment);
		CalculatedBottomLeft = AlignedCenter - AlignedHalfGrid;
	}


	CenterLocation = AlignedCenter;

	return CalculatedBottomLeft;
}

FRotator AGrid::GetTriangleRotation(const int XIndex, const int YIndex)
{
	const bool IsXEven = UGridFunctionLibrary::IsFloatEven(XIndex);
	const bool IsYEven = UGridFunctionLibrary::IsFloatEven(YIndex);

	FRotator TriangleRotation = FRotator(0, 0, 0);
	if ((IsXEven && !IsYEven) || (!IsXEven && IsYEven))
	{
		TriangleRotation.Yaw = 180;
	}

	return TriangleRotation;
}

FVector AGrid::GetTileLocation(const int XIndex, const int YIndex) const
{
	float AlignedXIndex = XIndex;
	float AlignedYIndex = YIndex;
	if (Shape == EGridShapeEnum::Triangle)
	{
		AlignedYIndex = AlignedYIndex / 2;
	}
	else if (Shape == EGridShapeEnum::Hexagon)
	{
		AlignedXIndex = AlignedXIndex * .75;
		AlignedYIndex = AlignedYIndex * .5;
	}


	const FVector IndexVector = FVector(AlignedXIndex, AlignedYIndex, 0);
	const FVector SizedVector = IndexVector * TileSize;
	const FVector TileLocation = BottomLeft + SizedVector;

	return TileLocation;
}

FTraceGroundResult AGrid::TraceBoardGround(const FVector& Location) const
{
	const FVector StartVector = Location + FVector(0, 0, 1000);
	const FVector EndVector = Location - FVector(0, 0, 1000);

	int RadiusReduction = 3;
	if (Shape == EGridShapeEnum::Triangle)
	{
		RadiusReduction = 5;
	}

	const float SphereRadius = TileSize.X / RadiusReduction;
	const FCollisionShape Sphere = FCollisionShape::MakeSphere(SphereRadius);

	TArray<FHitResult> HitResultArray;
	const bool HasHit = GetWorld()->SweepMultiByChannel(HitResultArray, StartVector, EndVector, FQuat::Identity, ECC_GameTraceChannel1, Sphere);

	FTraceGroundResult Result;
	Result.HasHitSomething = HasHit;

	if (HasHit)
	{
		ETileTypeEnum TileType = ETileTypeEnum::Normal;
		for (FHitResult HitResult : HitResultArray)
		{
			AActor* HitActor = HitResult.GetActor();
			if (AGridModifier* GridModifier = Cast<AGridModifier>(HitActor))
			{
				TileType = GridModifier->GetTileType();
			}
			else
			{
				//float AlignedZ = FMath::GridSnap(HitResult.ImpactPoint.Z, TileSize.Z);
				Result.FoundLocation = FVector(Location.X, Location.Y, HitResult.ImpactPoint.Z + GroundHeightOffset);
			}
		}
		Result.TileType = TileType;
	}
	else
	{
		Result.FoundLocation = Location + FVector(0, 0, GroundHeightOffset);
	}

	return Result;
}

void AGrid::AddGridTile(const FTileDataStruct& TileData)
{
	TileDataMap.Add(TileData.Index, TileData);

	GridVisualComponent->UpdateTileVisual(TileData);
	UpdateDebugTileInfo(TileData.Index);
}

void AGrid::RemoveGridTile(const FIntPoint Index)
{
	if (TileDataMap.Remove(Index))
	{
		const FTileDataStruct TileData
		{
			.Index = FIntPoint(Index.X, Index.Y),
			.TileType = ETileTypeEnum::None,
		};
		GridVisualComponent->UpdateTileVisual(TileData);
		UpdateDebugTileInfo(Index);
	}
}

FRotator AGrid::GetTileRotation(const int XIndex, const int YIndex) const
{
	FRotator Rotation = FRotator(0, 0, 0);
	if (Shape == EGridShapeEnum::Triangle)
	{
		Rotation = GetTriangleRotation(XIndex, YIndex);
	}

	return Rotation;
}

FVector AGrid::GetTileScale()
{
	const FGridShapeDataStruct* GridShapeData = GetGridShapeData();

	const FVector MeshSize = GridShapeData->MeshSize;
	const FVector TileScale = TileSize / MeshSize;

	return TileScale;
}

void AGrid::SpawnGenericTiles(const FGridShapeDataStruct* GridShapeData, bool UseEnvironment)
{
	for (int X = 0; X < TileCount.X; X++)
	{
		for (int Y = 0; Y < TileCount.Y; Y++)
		{
			FVector TileLocation = GetTileLocation(X, Y);
			FRotator Rotation = GetTileRotation(X, Y);
			FVector TileScale = GetTileScale();

			FTransform TileTransform = FTransform();
			TileTransform.SetLocation(TileLocation);
			TileTransform.SetRotation(Rotation.Quaternion());
			TileTransform.SetScale3D(TileScale);

			if (UseEnvironment)
			{
				FTraceGroundResult Result = TraceBoardGround(TileLocation);
				TileTransform.SetLocation(Result.FoundLocation);

				FTileDataStruct TileData
				{
					.Index = FIntPoint(X, Y),
					.TileType = Result.TileType,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
			else
			{
				FTileDataStruct TileData
				{
					.Index = FIntPoint(X, Y),
					.TileType = ETileTypeEnum::Normal,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
		}
	}
}

void AGrid::SpawnHexTiles(const FGridShapeDataStruct* GridShapeData, bool UseEnvironment)
{
	int YMaxCount = TileCount.Y * 2;
	for (int X = 0; X < TileCount.X; X++)
	{
		int StartingY;
		if (X % 2 == 0)
		{
			StartingY = 0;
		}
		else
		{
			StartingY = 1;
		}

		for (int Y = StartingY; Y < YMaxCount; Y++)
		{
			Y++;

			FVector TileLocation = GetTileLocation(X, Y);
			FRotator Rotation = GetTileRotation(X, Y);
			FVector TileScale = GetTileScale();

			FTransform TileTransform = FTransform();
			TileTransform.SetLocation(TileLocation);
			TileTransform.SetRotation(Rotation.Quaternion());
			TileTransform.SetScale3D(TileScale);

			if (UseEnvironment)
			{
				FTraceGroundResult Result = TraceBoardGround(TileLocation);
				TileTransform.SetLocation(Result.FoundLocation);

				FTileDataStruct TileData
				{
					.Index = FIntPoint(X, Y),
					.TileType = Result.TileType,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
			else
			{
				FTileDataStruct TileData
				{
					.Index = FIntPoint(X, Y),
					.TileType = ETileTypeEnum::Normal,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
		}
	}
}

void AGrid::SpawnGrid(const bool UseEnvironment)
{
	TileDataMap.Empty();
	GridVisualComponent->DestroyGridVisual();
	if (GridDebugComponent)
	{
		GridDebugComponent->ClearTileTextActorMap();
	}

	if (Shape != EGridShapeEnum::None)
	{
		if (const FGridShapeDataStruct* GridShapeData = SetStaticMeshValues())
		{
			BottomLeft = FindGridBottomLeftLocation();

			if (Shape == EGridShapeEnum::Hexagon)
			{
				SpawnHexTiles(GridShapeData, UseEnvironment);
			}
			else
			{
				SpawnGenericTiles(GridShapeData, UseEnvironment);
			}
		}
	}
	SpawningGrid = false;
}

FVector AGrid::GetGridCursorLocation() const
{
	if (const APlayerController* PlayerController = UGameplayStatics::GetPlayerController(this, 0))
	{
		FHitResult MouseHitResult;
		const bool bIsHit = PlayerController->GetHitResultUnderCursor(ECC_GameTraceChannel2, false, MouseHitResult);

		if (bIsHit)
		{
			return MouseHitResult.Location;
		}

		FHitResult MouseHitGroundResult;
		const bool bIsHitGround = PlayerController->GetHitResultUnderCursor(ECC_GameTraceChannel1, false, MouseHitGroundResult);

		if (bIsHitGround)
		{
			return MouseHitGroundResult.Location;
		}

		float MouseX, MouseY;
		PlayerController->GetMousePosition(MouseX, MouseY);

		FVector WorldLocation, WorldDirection;
		PlayerController->DeprojectScreenPositionToWorld(MouseX, MouseY, WorldLocation, WorldDirection);

		const FVector LineEnd = (WorldDirection * 9999999) + WorldLocation;

		const FVector Normal = FVector::UpVector;
		const FPlane LinePlane(CenterLocation, Normal);

		const FVector IntersectionPoint = FMath::RayPlaneIntersection(WorldLocation, LineEnd, LinePlane);
		return IntersectionPoint;
	}

	return FVector(-999, -999, -999);
}

FIntPoint AGrid::GetSquareTileIndex(const FVector& GridLocation) const
{
	const FVector SnappedVector = UGridFunctionLibrary::SnapVectorToVector(GridLocation, TileSize);
	const FIntPoint SnappedGridLocation = FIntPoint(SnappedVector.X, SnappedVector.Y);
	const FIntPoint TileIndex = SnappedGridLocation / FIntPoint(TileSize.X, TileSize.Y);

	return TileIndex;
}

FIntPoint AGrid::GetTriangleTileIndex(const FVector& GridLocation) const
{
	const FVector TriangleTileDivider = TileSize / FVector(1, 2, 1);
	const FVector SnappedVector = UGridFunctionLibrary::SnapVectorToVector(GridLocation, TriangleTileDivider);

	const FVector2D SnappedGridLocation = FVector2D(SnappedVector.X, SnappedVector.Y);
	const FVector2D BasicTileIndex = SnappedGridLocation / FVector2D(TileSize.X, TileSize.Y);

	const FVector2D TileIndexVector = BasicTileIndex * FVector2D(1, 2);
	const FIntPoint TileIndex = FIntPoint(TileIndexVector.X, TileIndexVector.Y);

	return TileIndex;
}

FIntPoint AGrid::GetHexagonTileIndex(const FVector& GridLocation) const
{
	// Calculate the basic indices by reversing GetTileLocation transformations exactly
	const float RawXIndex = GridLocation.X / (TileSize.X * 0.75);
	const float RawYIndex = GridLocation.Y / (TileSize.Y * 0.5);

	// Get the base tile indices  
	int32 BasicXIndex = FMath::RoundToInt(RawXIndex);

	// For Y, we need to account for the hexagon offset pattern
	// The tiles are positioned at Y indices 1,3,5... for even columns and 2,4,6... for odd columns
	// But they're spaced every 1.0 in the RawYIndex space (which corresponds to 0.5 * TileSize.Y in world space)

	FIntPoint TileIndex;

	if (BasicXIndex % 2 == 0)
	{
		// Even columns: tiles at Y=1,3,5,7... 
		// These correspond to RawYIndex values at 1.0, 3.0, 5.0...
		// Find the closest odd integer
		int32 RoundedY = FMath::RoundToInt(RawYIndex);
		if (RoundedY % 2 == 0)
		{
			// If we rounded to an even number, adjust to nearest odd
			RoundedY += (RawYIndex > RoundedY) ? 1 : -1;
		}
		TileIndex = FIntPoint(BasicXIndex, RoundedY);
	}
	else
	{
		// Odd columns: tiles at Y=2,4,6,8...
		// Find the closest even integer  
		int32 RoundedY = FMath::RoundToInt(RawYIndex);
		if (RoundedY % 2 == 1)
		{
			// If we rounded to an odd number, adjust to nearest even
			RoundedY += (RawYIndex > RoundedY) ? 1 : -1;
		}
		TileIndex = FIntPoint(BasicXIndex, RoundedY);
	}

	return TileIndex;
}

FIntPoint AGrid::GetTileIndex(const FVector& WorldLocation) const
{
	const FVector GridLocation = WorldLocation - BottomLeft;

	FIntPoint TileIndex = FIntPoint(-999, -999);
	switch (Shape)
	{
	case EGridShapeEnum::Square:
		TileIndex = GetSquareTileIndex(GridLocation);
		break;
	case EGridShapeEnum::Triangle:
		TileIndex = GetTriangleTileIndex(GridLocation);
		break;
	case EGridShapeEnum::Hexagon:
		TileIndex = GetHexagonTileIndex(GridLocation);
		break;
	default: ;
	}

	return TileIndex;
}

void AGrid::AddTileState(const FIntPoint Index, const ETileStateEnum State)
{
	if (FTileDataStruct* TileData = TileDataMap.Find(Index))
	{
		const int IsNew = TileData->StateArray.AddUnique(State);

		if (IsNew >= 0)
		{
			GridVisualComponent->UpdateTileVisual(*TileData);
			UpdateDebugTileInfo(TileData->Index);
		}
	}
}

void AGrid::RemoveTileState(const FIntPoint Index, const ETileStateEnum State)
{
	if (FTileDataStruct* TileData = TileDataMap.Find(Index))
	{
		const int WasRemoved = TileData->StateArray.Remove(State);

		if (WasRemoved >= 0)
		{
			GridVisualComponent->UpdateTileVisual(*TileData);
			UpdateDebugTileInfo(Index);
		}
	}
}

TArray<FIntPoint> AGrid::GetAllTilesWithState(const ETileStateEnum State) const
{
	//TODO This doesnt feel efficient and may need a second pass
	TArray<FIntPoint> Ret;

	for (const TPair<FIntPoint, FTileDataStruct>& TilePair : TileDataMap)
	{
		FTileDataStruct Tile = TilePair.Value;
		TArray<ETileStateEnum> StateArray = Tile.StateArray;

		if (StateArray.Contains(State))
		{
			Ret.Add(Tile.Index);
		}
	}

	return Ret;
}

void AGrid::ClearStateFromTiles(const ETileStateEnum State)
{
	//TODO with this calling GetAllTilesWithState this feels like it loops 1 too many times. Need to relook at this in the future
	TArray<FIntPoint> IndexList = GetAllTilesWithState(State);

	for (const FIntPoint Index : IndexList)
	{
		RemoveTileState(Index, State);
	}
}

bool AGrid::IsIndexValid(const FIntPoint Index) const
{
	const bool bIsValid = TileDataMap.Contains(Index);
	return bIsValid;
}

FIntPoint AGrid::GetCursorTileIndex() const
{
	const FVector CursorLocation = GetGridCursorLocation();
	const FIntPoint TileIndex = GetTileIndex(CursorLocation);

	return TileIndex;
}

#pragma region Debug Information
void AGrid::UpdateDebugTileInfo(const FIntPoint Index)
{
	if (GridDebugComponent)
	{
		const FTileDataStruct* TileData = TileDataMap.Find(Index);
		GridDebugComponent->UpdateTileText(Index, TileData);
	}
}
#pragma endregion

// Getters and Setters Implementation

const FGridShapeDataStruct
*
AGrid::GetGridShapeData()
{
	if (!GridShapeDataTable)
	{
		return nullptr;
	}

	UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
	FString ShapeName = EnumPtr->GetNameStringByValue(static_cast<int64>(Shape));

	FName RowName = FName(*ShapeName);

	// Use the stored data table pointer
	const FGridShapeDataStruct* GridShapeData = GridShapeDataTable->FindRow<FGridShapeDataStruct>(
		RowName, TEXT("SpawnGrid"));
	return GridShapeData;
}

bool AGrid::IsSpawningGrid() const
{
	return SpawningGrid;
}

void AGrid::SetIsSpawningGridForDelay()
{
	SpawningGrid = true;
}

// CenterLocation
FVector AGrid::GetCenterLocation() const
{
	return CenterLocation;
}

void AGrid::SetCenterLocation(const FVector& NewCenterLocation)
{
	CenterLocation = NewCenterLocation;
}

FVector AGrid::GetBottomLeft() const
{
	return BottomLeft;
}

// TileSize
FVector AGrid::GetTileSize() const
{
	return TileSize;
}

void AGrid::SetTileSize(const FVector& NewTileSize)
{
	TileSize = NewTileSize;
}

// TileCount
FIntPoint AGrid::GetTileCount() const
{
	return TileCount;
}

void AGrid::SetTileCount(const FIntPoint& NewTileCount)
{
	TileCount = NewTileCount;
}

// Shape
EGridShapeEnum AGrid::GetShape() const
{
	return Shape;
}

void AGrid::SetShape(const EGridShapeEnum& NewShape)
{
	Shape = NewShape;
}

TMap<FIntPoint, FTileDataStruct> AGrid::GetTileDataMap()
{
	return TileDataMap;
}

void AGrid::SetTileTextDebug(const bool bIsTileTextDebugMode)
{
	if (GridDebugComponent)
	{
		GridDebugComponent->SetDebugEnabled(bIsTileTextDebugMode);

		if (!bIsTileTextDebugMode)
		{
			GridDebugComponent->ClearTileTextActorMap();
		}

		TArray<FIntPoint> IndexArray;
		TileDataMap.GetKeys(IndexArray);

		for (const FIntPoint Index : IndexArray)
		{
			UpdateDebugTileInfo(Index);
		}
	}
}

bool AGrid::GetTileTextDebug() const
{
	if (GridDebugComponent)
	{
		const bool bDebugEnabled = GridDebugComponent->GetDebugEnabled();
		return bDebugEnabled;
	}
	return false;
}

TArray<FIntPoint> AGrid::GetValidTileNeighbors(const FIntPoint Index) const
{
	TArray<FIntPoint> Ret;

	if (GridPathfindingComponent)
	{
		Ret = GridPathfindingComponent->GetValidTileNeighbors(TileDataMap, Shape, TileSize, Index);
	}

	return Ret;
}
