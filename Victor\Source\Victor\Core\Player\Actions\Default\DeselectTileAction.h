// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Victor/Core/Player/Actions/PlayerAction.h"
#include "DeselectTileAction.generated.h"

/**
 * 
 */
UCLASS()
class VICTOR_API UDeselectTileAction : public UObject, public IPlayerAction
{
	GENERATED_BODY()

public:
	virtual void Execute(UWorld* World) override;
	virtual bool CanExecute(UWorld* World) const override;
	virtual FString GetActionName() const override;
};
