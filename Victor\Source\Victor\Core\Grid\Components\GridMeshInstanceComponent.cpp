// Fill out your copyright notice in the Description page of Project Settings.


#include "GridMeshInstanceComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Victor/Core/Grid/Utilities/TileStateEnum.h"

// Sets default values
UGridMeshInstanceComponent::UGridMeshInstanceComponent()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryComponentTick.bCanEverTick = false;

	InstancedStaticMesh = CreateDefaultSubobject<UInstancedStaticMeshComponent>(TEXT("InstancedStaticMesh"));

	InstancedStaticMesh->NumCustomDataFloats = 4;
}

void UGridMeshInstanceComponent::BeginPlay()
{
	Super::BeginPlay();
}

void UGridMeshInstanceComponent::AddInstance(const FIntPoint Index, const FTransform& InstanceTransform, const TArray<ETileStateEnum>& StateArray)
{
	RemoveInstance(Index);
	int32 InstanceIndex = InstancedStaticMesh->AddInstance(InstanceTransform);

	InstanceIndexArray.Add(Index);

	if (InstanceIndex != INDEX_NONE)
	{
		FLinearColor StateColor = GetStateColor(StateArray);

		InstancedStaticMesh->SetCustomDataValue(InstanceIndex, 0, StateColor.R);
		InstancedStaticMesh->SetCustomDataValue(InstanceIndex, 1, StateColor.G);
		InstancedStaticMesh->SetCustomDataValue(InstanceIndex, 2, StateColor.B);

		float IsFilled;
		if (StateColor == FLinearColor::Black)
		{
			IsFilled = 0.0f;
		}
		else
		{
			IsFilled = 1.0f;
		}

		InstancedStaticMesh->SetCustomDataValue(InstanceIndex, 3, IsFilled);
	}
}

void UGridMeshInstanceComponent::RemoveInstance(FIntPoint Index)
{
	if (InstanceIndexArray.Contains(Index))
	{
		int ArrayIndex = InstanceIndexArray.IndexOfByKey(Index);
		InstancedStaticMesh->RemoveInstance(ArrayIndex);
		InstanceIndexArray.Remove(Index);
	}
}

void UGridMeshInstanceComponent::ClearInstances()
{
	InstancedStaticMesh->ClearInstances();
	InstanceIndexArray.Empty();
}

void UGridMeshInstanceComponent::InitializeGridMeshInstance(UStaticMesh* ShapeMesh, UMaterialInstance* ShapeMaterial, FLinearColor ShapeColor, ECollisionEnabled::Type Collision) const
{
	InstancedStaticMesh->SetStaticMesh(ShapeMesh);
	InstancedStaticMesh->SetMaterial(0, ShapeMaterial);

	FVector ColorVector = FVector(ShapeColor.R, ShapeColor.G, ShapeColor.B);
	InstancedStaticMesh->SetVectorParameterValueOnMaterials("Color", ColorVector);
	InstancedStaticMesh->SetCollisionEnabled(Collision);
}

void UGridMeshInstanceComponent::SetInstanceMeshCollision(ECollisionEnabled::Type NewCollisionEnabled,
                                                          FCollisionResponseContainer NewResponses) const
{
	if (InstancedStaticMesh)
	{
		InstancedStaticMesh->SetCollisionEnabled(NewCollisionEnabled);
		InstancedStaticMesh->SetCollisionResponseToChannels(NewResponses);
	}
}

FLinearColor UGridMeshInstanceComponent::GetStateColor(const TArray<ETileStateEnum>& StateArray)
{
	if (StateArray.Num() == 0)
	{
		return FLinearColor::Black;
	}

	if (StateArray.Contains(ETileStateEnum::Hovered))
	{
		return FLinearColor::Yellow;
	}

	if (StateArray.Contains(ETileStateEnum::Selected))
	{
		return FLinearColor::White;
	}

	if (StateArray.Contains(ETileStateEnum::Neighbor))
	{
		return FLinearColor::Blue;
	}

	//TODO This system is rough and hard coded. May need to rework

	return FLinearColor::Black;
}
