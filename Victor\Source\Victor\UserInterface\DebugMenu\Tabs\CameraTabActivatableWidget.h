// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "CameraTabActivatableWidget.generated.h"

class UDebugSliderActivatableWidget;
class AVictorPlayerPawn;

/**
 *
 */
UCLASS()
class VICTOR_API UCameraTabActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* MovementSpeedSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* MovementInterpSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* RotationAngleSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* RotationInterpSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* ZoomSpeedSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* ZoomInterpSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* ZoomMinSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderActivatableWidget* ZoomMaxSpinBox;

	AVictorPlayerPawn* VictorPlayerPawn;

	//Functions
private:
	void OnMovementSpeedSpinBoxValueChange( float NewValue );

	void OnMovementInterpSpinBoxValueChange( float NewValue );

	void OnRotationAngleSpinBoxValueChange( float NewValue );

	void OnRotationInterpSpinBoxValueChange( float NewValue );

	void OnZoomSpeedSpinBoxValueChange( float NewValue );

	void OnZoomInterpSpinBoxValueChange( float NewValue );

	void OnZoomMinSpinBoxValueChange( float NewValue );

	void OnZoomMaxSpinBoxValueChange( float NewValue );

public:
	void NativeOnInitialized() override;

	void NativeConstruct() override;
};
