// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugSliderVector3Widget.h"
#include "CommonTextBlock.h"
#include "Components/SpinBox.h"

void UDebugSliderVector3Widget::NativePreConstruct()
{
	Super::NativePreConstruct();

	if( DisplayName )
	{
		DisplayName->SetText( SliderName );
	}

	if( DebugXValueSpinBox )
	{
		DebugXValueSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugXValueSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugXValueSpinBox->SetMinValue( MinMaxValues.X );
		DebugXValueSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugXValueSpinBox->SetValue( Value.X );

		DebugXValueSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugXValueSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugXValueSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugXValueSpinBox->SetDelta( Delta );
	}

	if( DebugYValueSpinBox )
	{
		DebugYValueSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugYValueSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugYValueSpinBox->SetMinValue( MinMaxValues.X );
		DebugYValueSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugYValueSpinBox->SetValue( Value.Y );

		DebugYValueSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugYValueSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugYValueSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugYValueSpinBox->SetDelta( Delta );
	}

	if( DebugZValueSpinBox )
	{
		DebugZValueSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugZValueSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugZValueSpinBox->SetMinValue( MinMaxValues.X );
		DebugZValueSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugZValueSpinBox->SetValue( Value.Z );

		DebugZValueSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugZValueSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugZValueSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugZValueSpinBox->SetDelta( Delta );
	}
}

void UDebugSliderVector3Widget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( DebugXValueSpinBox )
	{
		DebugXValueSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderVector3Widget::OnSliderXValueChange );
	}

	if( DebugYValueSpinBox )
	{
		DebugYValueSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderVector3Widget::OnSliderYValueChange );
	}

	if( DebugZValueSpinBox )
	{
		DebugZValueSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderVector3Widget::OnSliderZValueChange );
	}
}

void UDebugSliderVector3Widget::OnSliderXValueChange( float NewValue )
{
	Value.X = NewValue;
	ValueChangeDelegate.ExecuteIfBound( Value );
}

void UDebugSliderVector3Widget::OnSliderYValueChange( float NewValue )
{
	Value.Y = NewValue;
	ValueChangeDelegate.ExecuteIfBound( Value );
}

void UDebugSliderVector3Widget::OnSliderZValueChange( float NewValue )
{
	Value.Z = NewValue;
	ValueChangeDelegate.ExecuteIfBound( Value );
}

FVector UDebugSliderVector3Widget::GetValue() const
{
	return Value;
}

void UDebugSliderVector3Widget::SetValue( FVector NewValue )
{
	Value = NewValue;

	if( DebugXValueSpinBox && DebugYValueSpinBox && DebugZValueSpinBox )
	{
		DebugXValueSpinBox->SetValue( Value.X );
		DebugYValueSpinBox->SetValue( Value.Y );
		DebugZValueSpinBox->SetValue( Value.Z );
	}
}

