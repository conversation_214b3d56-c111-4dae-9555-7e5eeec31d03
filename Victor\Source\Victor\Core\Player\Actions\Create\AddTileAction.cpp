// Fill out your copyright notice in the Description page of Project Settings.


#include "AddTileAction.h"

#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"

void UAddTileAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();

		const bool bIsValid = CurrentGrid->IsIndexValid(Index);
		if (!bIsValid)
		{
			const FVector TileLocation = CurrentGrid->GetTileLocation(Index.X, Index.Y);
			const FTraceGroundResult GroundResult = CurrentGrid->TraceBoardGround(TileLocation);

			const FVector TileGroundLocation = GroundResult.FoundLocation;
			const FRotator Rotation = CurrentGrid->GetTileRotation(Index.X, Index.Y);
			const FVector TileScale = CurrentGrid->GetTileScale();

			FTransform TileTransform = FTransform();
			TileTransform.SetLocation(TileGroundLocation);
			TileTransform.SetScale3D(TileScale);
			TileTransform.SetRotation(Rotation.Quaternion());

			const FTileDataStruct TileData
			{
				.Index = Index,
				.TileType = ETileTypeEnum::Normal,
				.Transform = TileTransform
			};

			CurrentGrid->AddGridTile(TileData);
		}
	}
}

bool UAddTileAction::CanExecute(UWorld* World) const
{
	return true;
}

FString UAddTileAction::GetActionName() const
{
	return TEXT("AddTileAction");
}
