﻿#pragma once

#include "PlayerActionTypes.generated.h"

UENUM(BlueprintType)
enum class EPlayerInputAction : uint8
{
	Primary UMETA(DisplayName = "Primary Action"),
	Secondary UMETA(DisplayName = "Secondary Action"),
	Continuous UMETA(DisplayName = "Continuous Action"),

	MAX UMETA(Hidden)
};

UENUM(BlueprintType)
enum class EPlayerActionContext : uint8
{
	None UMETA(DisplayName = "None"),
	Default UMETA(DisplayName = "Default"),
	Create UMETA(DisplayName = "Create"),
	Pathfinding UMETA(DisplayName = "Pathfinding"),

	MAX UMETA(Hidden)
};
