// Fill out your copyright notice in the Description page of Project Settings.


#include "GridWorldSubsystem.h"

#include "Kismet/GameplayStatics.h"
#include "Victor/Core/Grid/Grid.h"

void UGridWorldSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
}

void UGridWorldSubsystem::Deinitialize()
{
	Super::Deinitialize();
}

void UGridWorldSubsystem::PostInitialize()
{
	Super::PostInitialize();
	FindGridInWorld();
}

AGrid* UGridWorldSubsystem::GetGrid() const
{
	// Lazy load if needed
	if (!CurrentGrid.IsValid())
	{
		const_cast<UGridWorldSubsystem*>(this)->FindGridInWorld();
	}
	return CurrentGrid.Get();
}

void UGridWorldSubsystem::FindGridInWorld()
{
	if (UWorld* World = GetWorld())
	{
		CurrentGrid = Cast<AGrid>(UGameplayStatics::GetActorOfClass(World, AGrid::StaticClass()));
	}
}
