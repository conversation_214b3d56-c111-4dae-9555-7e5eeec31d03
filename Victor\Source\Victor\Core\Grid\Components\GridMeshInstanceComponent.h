// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Victor/Core/Grid/Utilities/TileStateEnum.h"
#include "GridMeshInstanceComponent.generated.h"

UCLASS()
class VICTOR_API UGridMeshInstanceComponent : public UActorComponent
{
	GENERATED_BODY()

	//Fields
	//Components
	UPROPERTY(VisibleAnywhere, Category = "Components")
	UInstancedStaticMeshComponent* InstancedStaticMesh;

	TArray<FIntPoint> InstanceIndexArray;

	//Functions
public:
	// Sets default values for this actor's properties
	UGridMeshInstanceComponent();

protected:
	virtual void BeginPlay() override;

public:
	void AddInstance(FIntPoint Index, const FTransform& InstanceTransform, const TArray<ETileStateEnum>& StateArray);

	void RemoveInstance(FIntPoint Index);

	void ClearInstances();

	void InitializeGridMeshInstance(UStaticMesh* ShapeMesh, UMaterialInstance* ShapeMaterial, FLinearColor ShapeColor, ECollisionEnabled::Type Collision) const;

	void SetInstanceMeshCollision(ECollisionEnabled::Type NewCollisionEnabled, FCollisionResponseContainer NewResponses) const;

	FLinearColor GetStateColor(const TArray<ETileStateEnum>& StateArray);
};
