// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "PlayerUiWidget.generated.h"

class UCommonActivatableWidgetStack;
class UCommonActivatableWidget;

/**
 *
 */
UCLASS()
class VICTOR_API UPlayerUiWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( EditAnywhere )
	TSubclassOf<UCommonActivatableWidget> DebugMenuUiClass;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonActivatableWidgetStack* DebugMenuStack;

	//Functions
public:
	void NativeOnInitialized() override;

	UCommonActivatableWidget* PushDebugUiStack();
};
