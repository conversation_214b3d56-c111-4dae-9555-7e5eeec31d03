// Fill out your copyright notice in the Description page of Project Settings.


#include "DeselectTileAction.h"

#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"

void UDeselectTileAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();

		CurrentGrid->RemoveTileState(Index, ETileStateEnum::Selected);
	}
}

bool UDeselectTileAction::CanExecute(UWorld* World) const
{
	return true;
}

FString UDeselectTileAction::GetActionName() const
{
	return TEXT("SelectTileAction");
}
