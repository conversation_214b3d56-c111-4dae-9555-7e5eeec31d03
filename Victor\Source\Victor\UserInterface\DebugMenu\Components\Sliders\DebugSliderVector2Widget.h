// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "DebugSliderVector2Widget.generated.h"

class UCommonTextBlock;
class USpinBox;

DECLARE_DELEGATE_OneParam( FSliderVector2ValueChangeDelegate, FVector2D );

/**
 *
 */
UCLASS()
class VICTOR_API UDebugSliderVector2Widget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FText SliderName;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxSliderValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D Value;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxFractDigits;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	bool AlwaysSnapToDelta;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	float Delta;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonTextBlock* DisplayName;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugXValueSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugYValueSpinBox;

public:
	FSliderVector2ValueChangeDelegate ValueChangeDelegate;

	//Functions
private:
	UFUNCTION()
	void OnSliderXValueChange( float NewValue );

	UFUNCTION()
	void OnSliderYValueChange( float NewValue );

public:
	void NativePreConstruct() override;

	void NativeOnInitialized() override;

	FVector2D GetValue() const;
	void SetValue( FVector2D NewValue );
};
