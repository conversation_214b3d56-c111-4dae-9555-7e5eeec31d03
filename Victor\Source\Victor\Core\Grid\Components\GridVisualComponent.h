// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GridVisualComponent.generated.h"

struct FTileDataStruct;

class UGridMeshInstanceComponent;
class AGrid;

UCLASS()
class VICTOR_API UGridVisualComponent : public UActorComponent
{
	GENERATED_BODY()


	//Fields
	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridMeshInstanceComponent* GridMeshInstanceComponent;

	UPROPERTY(EditAnywhere, Category = Spawn)
	float GroundOffset = 2;

	AGrid* CurrentGrid;

	//Functions
public:
	// Sets default values for this actor's properties
	UGridVisualComponent();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	void InitializeGridVisual(AGrid* Grid);

	void DestroyGridVisual();

	void UpdateTileVisual(const FTileDataStruct& TileData) const;

	void SetGroundOffset(float NewGroundOffset);

	void SetInstanceMeshCollision(ECollisionEnabled::Type NewCollisionEnabled,
	                              FCollisionResponseContainer NewResponses) const;
};
