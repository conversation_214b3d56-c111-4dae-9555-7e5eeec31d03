// Fill out your copyright notice in the Description page of Project Settings.


#include "PathfindingActivatableWidget.h"
#include "Components/CheckBox.h"
#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"

void UPathfindingActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if (ShowTileTextCheckBox)
	{
		ShowTileTextCheckBox->OnCheckStateChanged.AddDynamic(this, &UPathfindingActivatableWidget::OnShowTileTextValueChange);
	}
}

void UPathfindingActivatableWidget::NativeConstruct()
{
	Super::NativeConstruct();

	const UGridWorldSubsystem* GridWorldSubsystem = GetWorld()->GetSubsystem<UGridWorldSubsystem>();
	CurrentGrid = GridWorldSubsystem->GetGrid();

	if (ShowTileTextCheckBox)
	{
		const bool bTileTextEnabled = CurrentGrid->GetTileTextDebug();
		ShowTileTextCheckBox->SetIsChecked(bTileTextEnabled);
	}
}

void UPathfindingActivatableWidget::OnShowTileTextValueChange(const bool bIsChecked)
{
	if (CurrentGrid)
	{
		CurrentGrid->SetTileTextDebug(bIsChecked);
	}
}
