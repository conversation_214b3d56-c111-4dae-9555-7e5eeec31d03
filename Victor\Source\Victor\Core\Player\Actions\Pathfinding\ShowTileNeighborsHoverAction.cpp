// Fill out your copyright notice in the Description page of Project Settings.


#include "ShowTileNeighborsHoverAction.h"

#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"
#include "Victor/Core/Grid/Utilities/TileStateEnum.h"

void UShowTileNeighborsHoverAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();

		if (HoveredIndex != Index)
		{
			CurrentGrid->ClearStateFromTiles(ETileStateEnum::Neighbor);
			CurrentGrid->RemoveTileState(HoveredIndex, ETileStateEnum::Hovered);

			HoveredIndex = Index;
			CurrentGrid->AddTileState(HoveredIndex, ETileStateEnum::Hovered);

			TArray<FIntPoint> NeighborList = CurrentGrid->GetValidTileNeighbors(HoveredIndex);
			for (const FIntPoint Neighbor : NeighborList)
			{
				CurrentGrid->AddTileState(Neighbor, ETileStateEnum::Neighbor);
			}
		}
	}
}

bool UShowTileNeighborsHoverAction::CanExecute(UWorld* World) const
{
	return true;
}

FString UShowTileNeighborsHoverAction::GetActionName() const
{
	return TEXT("SelectTileAction");
}
