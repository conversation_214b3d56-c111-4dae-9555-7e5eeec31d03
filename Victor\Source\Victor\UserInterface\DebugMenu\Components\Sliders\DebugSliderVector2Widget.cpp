// Fill out your copyright notice in the Description page of Project Settings.


#include "DebugSliderVector2Widget.h"
#include "CommonTextBlock.h"
#include "Components/SpinBox.h"

void UDebugSliderVector2Widget::NativePreConstruct()
{
	Super::NativePreConstruct();

	if( DisplayName )
	{
		DisplayName->SetText( SliderName );
	}

	if( DebugXValueSpinBox )
	{
		DebugXValueSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugXValueSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugXValueSpinBox->SetMinValue( MinMaxValues.X );
		DebugXValueSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugXValueSpinBox->SetValue( Value.X );

		DebugXValueSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugXValueSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugXValueSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugXValueSpinBox->SetDelta( Delta );
	}

	if( DebugYValueSpinBox )
	{
		DebugYValueSpinBox->SetMinSliderValue( MinMaxSliderValues.X );
		DebugYValueSpinBox->SetMaxSliderValue( MinMaxSliderValues.Y );

		DebugYValueSpinBox->SetMinValue( MinMaxValues.X );
		DebugYValueSpinBox->SetMaxValue( MinMaxValues.Y );

		DebugYValueSpinBox->SetValue( Value.Y );

		DebugYValueSpinBox->SetMinFractionalDigits( ( int32 )MinMaxFractDigits.X );
		DebugYValueSpinBox->SetMaxFractionalDigits( ( int32 )MinMaxFractDigits.Y );

		DebugYValueSpinBox->SetAlwaysUsesDeltaSnap( AlwaysSnapToDelta );
		DebugYValueSpinBox->SetDelta( Delta );
	}
}

void UDebugSliderVector2Widget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if( DebugXValueSpinBox )
	{
		DebugXValueSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderVector2Widget::OnSliderXValueChange );
	}

	if( DebugYValueSpinBox )
	{
		DebugYValueSpinBox->OnValueChanged.AddDynamic( this, &UDebugSliderVector2Widget::OnSliderYValueChange );
	}
}

void UDebugSliderVector2Widget::OnSliderXValueChange( float NewValue )
{
	Value.X = NewValue;
	ValueChangeDelegate.ExecuteIfBound( Value );
}

void UDebugSliderVector2Widget::OnSliderYValueChange( float NewValue )
{
	Value.Y = NewValue;
	ValueChangeDelegate.ExecuteIfBound( Value );
}

FVector2D UDebugSliderVector2Widget::GetValue() const
{
	return Value;
}

void UDebugSliderVector2Widget::SetValue( FVector2D NewValue )
{
	Value = NewValue;

	if( DebugXValueSpinBox && DebugYValueSpinBox )
	{
		DebugXValueSpinBox->SetValue( Value.X );
		DebugYValueSpinBox->SetValue( Value.Y );
	}
}
