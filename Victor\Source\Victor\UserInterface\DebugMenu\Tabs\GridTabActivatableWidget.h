// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Framework/SlateDelegates.h"
#include "GridTabActivatableWidget.generated.h"

class UPlayerActionContextSubsystem;
class AGrid;
class UBoardLevelLoader;
class UComboBoxString;
class UDebugSliderVector3Widget;
class UDebugSliderVector2Widget;
class UCheckBox;
class UCommonTextBlock;

/**
 *
 */
UCLASS()
class VICTOR_API UGridTabActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
	TObjectPtr<UPlayerActionContextSubsystem> ContextManager;

	UBoardLevelLoader* BoardLevelLoader;

	AGrid* CurrentGrid;

	FTimerHandle DelayTimerHandle;

	FTimerHandle DebugTimerHandle;

	float DrawUpdateTime = 0.1f;

protected:
	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UComboBoxString* BoardContextComboBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UComboBoxString* BoardLevelComboBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UComboBoxString* ShapeComboBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UDebugSliderVector3Widget* LocationSpinBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UDebugSliderVector2Widget* TileCountSpinBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UDebugSliderVector3Widget* TileSizeSpinBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCheckBox* UseEnvironmentCheckBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCheckBox* BoundsCheckBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCheckBox* CenterCheckBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCommonTextBlock* CenterTextBlock;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCheckBox* BottomLeftCheckBox;

	UPROPERTY(meta = ( BindWidget ), BlueprintReadOnly)
	UCommonTextBlock* BottomLeftTextBlock;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly)
	UCheckBox* MouseLocationCheckBox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly)
	UCommonTextBlock* MouseLocationTextBlock;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly)
	UCheckBox* HoveredTileCheckBox;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly)
	UCommonTextBlock* HoveredTileTextBlock;

	//Functions
private:
	UFUNCTION()
	void OnBoardContextComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType);

	UFUNCTION()
	void OnBoardLevelComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType);

	UFUNCTION()
	void OnShapeComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType);

	void OnLocationSpinBoxChange(FVector NewValue);

	void OnTileCountSpinBoxChange(FVector2D NewValue);

	void OnTileSizeSpinBoxChange(FVector NewValue);

	UFUNCTION()
	void OnUseEnvironmentValueChange(bool bIsChecked);

	void HandleGrid();

	void DelayComplete();

	void DrawDebugLines();

public:
	virtual void NativeOnInitialized() override;

	virtual void NativeConstruct() override;
};
