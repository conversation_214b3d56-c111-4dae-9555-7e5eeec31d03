// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "PlayerActionsComponent.generated.h"

class UPlayerActionContextSubsystem;
class AGrid;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class VICTOR_API UPlayerActionsComponent : public UActorComponent
{
	GENERATED_BODY()

	//Fields
	UPROPERTY()
	TObjectPtr<UPlayerActionContextSubsystem> ContextManager;

	//Functions
public:
	// Sets default values for this component's properties
	UPlayerActionsComponent();

private:
	void UpdateCursorTile() const;

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	void PrimaryAction() const;

	void SecondaryAction() const;
};
