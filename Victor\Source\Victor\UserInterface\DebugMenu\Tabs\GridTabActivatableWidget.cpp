// Fill out your copyright notice in the Description page of Project Settings.


#include "GridTabActivatableWidget.h"
#include "Kismet/GameplayStatics.h"
#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/LevelLoader/BoardLevelLoader.h"
#include "Components/ComboBoxString.h"
#include "UObject/EnumProperty.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Victor/UserInterface/DebugMenu/Components/Sliders/DebugSliderVector3Widget.h"
#include "Victor/UserInterface/DebugMenu/Components/Sliders/DebugSliderVector2Widget.h"
#include "Components/CheckBox.h"
#include "CommonTextBlock.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Victor/Core/Player/Subsystems/PlayerActionContextSubsystem.h"
#include "Victor/Core/Player/Types/PlayerActionTypes.h"

void UGridTabActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if (BoardContextComboBox)
	{
		BoardContextComboBox->OnSelectionChanged.AddDynamic(this, &UGridTabActivatableWidget::OnBoardContextComboBoxChange);

		const UEnum* ContextEnumPtr = StaticEnum<EPlayerActionContext>();
		const FString NoneName = ContextEnumPtr->GetNameStringByValue(static_cast<int64>(EPlayerActionContext::None));
		const FString DefaultName = ContextEnumPtr->GetNameStringByValue(static_cast<int64>(EPlayerActionContext::Default));
		const FString CreateName = ContextEnumPtr->GetNameStringByValue(static_cast<int64>(EPlayerActionContext::Create));
		const FString PathfindingName = ContextEnumPtr->GetNameStringByValue(static_cast<int64>(EPlayerActionContext::Pathfinding));

		BoardContextComboBox->AddOption(NoneName);
		BoardContextComboBox->AddOption(DefaultName);
		BoardContextComboBox->AddOption(CreateName);
		BoardContextComboBox->AddOption(PathfindingName);
	}

	if (BoardLevelComboBox)
	{
		BoardLevelComboBox->OnSelectionChanged.AddDynamic(this, &UGridTabActivatableWidget::OnBoardLevelComboBoxChange);
	}

	if (ShapeComboBox)
	{
		ShapeComboBox->OnSelectionChanged.AddDynamic(this, &UGridTabActivatableWidget::OnShapeComboBoxChange);

		UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
		FString NoneName = EnumPtr->GetNameStringByValue(static_cast<int64>(EGridShapeEnum::None));
		FString SquareName = EnumPtr->GetNameStringByValue(static_cast<int64>(EGridShapeEnum::Square));
		FString TriangleName = EnumPtr->GetNameStringByValue(static_cast<int64>(EGridShapeEnum::Triangle));
		FString HexagonName = EnumPtr->GetNameStringByValue(static_cast<int64>(EGridShapeEnum::Hexagon));

		ShapeComboBox->AddOption(NoneName);
		ShapeComboBox->AddOption(SquareName);
		ShapeComboBox->AddOption(TriangleName);
		ShapeComboBox->AddOption(HexagonName);
	}

	if (LocationSpinBox)
	{
		LocationSpinBox->ValueChangeDelegate.BindUObject(this, &UGridTabActivatableWidget::OnLocationSpinBoxChange);
	}

	if (TileCountSpinBox)
	{
		TileCountSpinBox->ValueChangeDelegate.BindUObject(this, &UGridTabActivatableWidget::OnTileCountSpinBoxChange);
	}

	if (TileSizeSpinBox)
	{
		TileSizeSpinBox->ValueChangeDelegate.BindUObject(this, &UGridTabActivatableWidget::OnTileSizeSpinBoxChange);
	}

	if (UseEnvironmentCheckBox)
	{
		UseEnvironmentCheckBox->OnCheckStateChanged.AddDynamic(this, &UGridTabActivatableWidget::OnUseEnvironmentValueChange);
	}
}

void UGridTabActivatableWidget::NativeConstruct()
{
	Super::NativeConstruct();

	APawn* PlayerPawn = UGameplayStatics::GetPlayerPawn(GetWorld(), 0);
	if (const APlayerController* PlayerController = Cast<APlayerController>(PlayerPawn->GetController()))
	{
		if (ULocalPlayer* LocalPlayer = PlayerController->GetLocalPlayer())
		{
			ContextManager = LocalPlayer->GetSubsystem<UPlayerActionContextSubsystem>();
			EPlayerActionContext BoardContext = ContextManager->GetCurrentContext();

			UEnum* ContextEnumPtr = StaticEnum<EPlayerActionContext>();
			FString ContextName = ContextEnumPtr->GetNameStringByValue(static_cast<int64>(BoardContext));
			BoardContextComboBox->SetSelectedOption(ContextName);
		}
	}

	UGridWorldSubsystem* GridWorldSubsystem = GetWorld()->GetSubsystem<UGridWorldSubsystem>();
	CurrentGrid = GridWorldSubsystem->GetGrid();

	BoardLevelLoader = GetGameInstance()->GetSubsystem<UBoardLevelLoader>();

	if (BoardLevelComboBox && BoardLevelLoader)
	{
		TArray<FString> LevelOptions = BoardLevelLoader->GetGameLevelNamesForDropdown();
		for (FString Level : LevelOptions)
		{
			BoardLevelComboBox->AddOption(Level);
		}
		BoardLevelComboBox->SetSelectedOption(LevelOptions[0]);
	}

	EGridShapeEnum Shape = CurrentGrid->GetShape();

	if (Shape == EGridShapeEnum::None)
	{
		Shape = EGridShapeEnum::Square;
	}

	UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
	FString ShapeName = EnumPtr->GetNameStringByValue(static_cast<int64>(Shape));
	ShapeComboBox->SetSelectedOption(ShapeName);

	FVector Location = CurrentGrid->GetCenterLocation();
	FVector2D TileCount = CurrentGrid->GetTileCount();
	FVector TileSize = CurrentGrid->GetTileSize();

	LocationSpinBox->SetValue(Location);
	TileCountSpinBox->SetValue(TileCount);
	TileSizeSpinBox->SetValue(CurrentGrid->GetTileSize());

	CurrentGrid->SpawnGrid(true);
	GetWorld()->GetTimerManager().SetTimer(DebugTimerHandle, this, &UGridTabActivatableWidget::DrawDebugLines, DrawUpdateTime, true);
}

void UGridTabActivatableWidget::OnBoardContextComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType)
{
	if (ContextManager)
	{
		EPlayerActionContext BoardContext;

		UEnum* EnumPtr = StaticEnum<EPlayerActionContext>();
		if (!EnumPtr)
		{
			BoardContext = EPlayerActionContext::None;
		}

		int64 Value = EnumPtr->GetValueByNameString(NewValue);

		// Check if a valid enum value was found
		if (Value != INDEX_NONE)
		{
			BoardContext = static_cast<EPlayerActionContext>(Value);
		}
		else
		{
			// Handle case where the string does not match any enum value
			UE_LOG(LogTemp, Warning, TEXT( "Failed to convert string '%s' to EGridShapeEnum. Setting to None." ), *NewValue);
			BoardContext = EPlayerActionContext::None;
		}

		ContextManager->SetContext(BoardContext);
	}
}

void UGridTabActivatableWidget::OnBoardLevelComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType)
{
	if (BoardLevelLoader)
	{
		int32 SelectedIndex = BoardLevelComboBox->GetSelectedIndex();
		BoardLevelLoader->LoadLevelByIndex(SelectedIndex);
	}
}

void UGridTabActivatableWidget::OnShapeComboBoxChange(FString NewValue, ESelectInfo::Type SelectionType)
{
	if (CurrentGrid)
	{
		EGridShapeEnum NewShape;

		const UEnum* EnumPtr = FindObject<UEnum>(nullptr, TEXT("/Script/Victor.EGridShapeEnum"));
		if (!EnumPtr)
		{
			NewShape = EGridShapeEnum::None;
		}

		int64 Value = EnumPtr->GetValueByNameString(NewValue);

		// Check if a valid enum value was found
		if (Value != INDEX_NONE)
		{
			NewShape = static_cast<EGridShapeEnum>(Value);
		}
		else
		{
			// Handle case where the string does not match any enum value
			UE_LOG(LogTemp, Warning, TEXT( "Failed to convert string '%s' to EGridShapeEnum. Setting to None." ),
			       *NewValue);
			NewShape = EGridShapeEnum::None;
		}

		CurrentGrid->SetShape(NewShape);
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnLocationSpinBoxChange(FVector NewValue)
{
	if (CurrentGrid)
	{
		CurrentGrid->SetCenterLocation(NewValue);
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnTileCountSpinBoxChange(FVector2D NewValue)
{
	if (CurrentGrid)
	{
		FIntPoint TileCount;
		TileCount.X = static_cast<int32>(NewValue.X);
		TileCount.Y = static_cast<int32>(NewValue.Y);

		CurrentGrid->SetTileCount(TileCount);
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnTileSizeSpinBoxChange(FVector NewValue)
{
	if (CurrentGrid)
	{
		CurrentGrid->SetTileSize(NewValue);
		HandleGrid();
	}
}

void UGridTabActivatableWidget::OnUseEnvironmentValueChange(bool bIsChecked)
{
	if (UseEnvironmentCheckBox)
	{
		HandleGrid();
	}
}

void UGridTabActivatableWidget::HandleGrid()
{
	if (CurrentGrid && !CurrentGrid->IsSpawningGrid())
	{
		CurrentGrid->SetIsSpawningGridForDelay();
		GetWorld()->GetTimerManager().SetTimer(DelayTimerHandle, this, &UGridTabActivatableWidget::DelayComplete, 0.05f,
		                                       false);
	}
}

void UGridTabActivatableWidget::DelayComplete()
{
	bool bIsUseEnvironment = UseEnvironmentCheckBox->IsChecked();
	CurrentGrid->SpawnGrid(bIsUseEnvironment);
}

void UGridTabActivatableWidget::DrawDebugLines()
{
	if (CurrentGrid)
	{
		FString CenterLocationString = CurrentGrid->GetCenterLocation().ToString();
		FText CenterLocationText = FText::FromString(CenterLocationString);
		CenterTextBlock->SetText(CenterLocationText);

		FString BottomLeftString = CurrentGrid->GetBottomLeft().ToString();
		FText BottomLeftText = FText::FromString(BottomLeftString);
		BottomLeftTextBlock->SetText(BottomLeftText);

		FString MouseLocationString = CurrentGrid->GetGridCursorLocation().ToString();
		FText MouseLocationText = FText::FromString(MouseLocationString);
		MouseLocationTextBlock->SetText(MouseLocationText);


		FIntPoint TileIndex = CurrentGrid->GetCursorTileIndex();
		FString HoveredTileString = TileIndex.ToString();
		FText HoveredTileText = FText::FromString(HoveredTileString);
		HoveredTileTextBlock->SetText(HoveredTileText);

		if (BoundsCheckBox->IsChecked())
		{
			FVector ExtentVector = CurrentGrid->GetCenterLocation() - CurrentGrid->GetBottomLeft();
			DrawDebugBox(GetWorld(), CurrentGrid->GetCenterLocation(), ExtentVector, FColor::Yellow, false,
			             DrawUpdateTime);
		}

		if (CenterCheckBox->IsChecked())
		{
			float radius = 100;
			int segments = 3;
			DrawDebugSphere(GetWorld(), CurrentGrid->GetCenterLocation(), radius, segments, FColor::Orange, false,
			                DrawUpdateTime);
			DrawDebugSphere(GetWorld(), LocationSpinBox->GetValue(), radius, segments, FColor::Yellow, false,
			                DrawUpdateTime);
		}

		if (BottomLeftCheckBox->IsChecked())
		{
			float radius = 100;
			int segments = 3;
			DrawDebugSphere(GetWorld(), CurrentGrid->GetBottomLeft(), radius, segments, FColor::Red, false,
			                DrawUpdateTime);
		}

		if (MouseLocationCheckBox->IsChecked())
		{
			float radius = 15;
			int segments = 5;
			DrawDebugSphere(GetWorld(), CurrentGrid->GetGridCursorLocation(), radius, segments, FColor::Yellow, false,
			                DrawUpdateTime);
		}


		if (HoveredTileCheckBox->IsChecked())
		{
			float radius = 15;
			int segments = 5;

			TMap<FIntPoint, FTileDataStruct> TileMap = CurrentGrid->GetTileDataMap();
			FTileDataStruct* TileData = TileMap.Find(TileIndex);

			if (TileData)
			{
				DrawDebugBox(GetWorld(), TileData->Transform.GetLocation(), FVector(35, 35, 5), FColor::Red, false,
				             DrawUpdateTime);
			}
		}
	}
}
