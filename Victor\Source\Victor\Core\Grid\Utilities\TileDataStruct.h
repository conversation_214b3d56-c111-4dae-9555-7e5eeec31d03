// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TileStateEnum.h"
#include "TileDataStruct.generated.h"

USTRUCT(BlueprintType)
struct VICTOR_API FTileDataStruct
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FIntPoint Index;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	ETileTypeEnum TileType;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FTransform Transform;
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	TArray<ETileStateEnum> StateArray;
};
