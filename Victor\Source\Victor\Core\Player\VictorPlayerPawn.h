// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "InputActionValue.h"
#include "VictorPlayerPawn.generated.h"

// Forward declarations
class UCameraComponent;
class USpringArmComponent;
class AVictorPlayerController;
class UInputMappingContext;
class UInputAction;
class UEnhancedInputLocalPlayerSubsystem;
class UPlayerActionsComponent;
class AGrid;

UCLASS()
class VICTOR_API AVictorPlayerPawn : public APawn
{
	GENERATED_BODY()

	//Fields
	//Components
	UPROPERTY(VisibleAnywhere, Category = "Components")
	UCameraComponent* Camera;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	USpringArmComponent* SpringArm;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	UPlayerActionsComponent* PlayerActionsComponent;

	//Input
	UPROPERTY(EditAnywhere, Category = Input)
	UInputMappingContext* DefaultPlayerContext;

	UPROPERTY(EditAnywhere, Category = Input)
	UInputAction* ZoomAction;

	UPROPERTY(EditAnywhere, Category = Input)
	UInputAction* BattleCamAction;

	UPROPERTY(EditAnywhere, Category = Input)
	UInputAction* RotateAction;

	UPROPERTY(EditAnywhere, Category = Input)
	UInputAction* PrimaryAction;

	UPROPERTY(EditAnywhere, Category = Input)
	UInputAction* SecondaryAction;

	//Pawn Values

#pragma region Zoom Values
	UPROPERTY(EditAnywhere, Category = CameraControls)
	float ZoomMultiplier = 100;

	UPROPERTY(EditAnywhere, Category = CameraControls)
	float MinZoomValue = 0;

	UPROPERTY(EditAnywhere, Category = CameraControls)
	float MaxZoomValue = 1000;

	UPROPERTY(EditAnywhere, Category = CameraControls)
	float ZoomInterpSpeed = 2.0;

	float DesiredZoom = 0;
#pragma endregion

#pragma region MovementValues
	UPROPERTY(EditAnywhere, Category = CameraControls)
	float MovementMultiplier = 40;

	UPROPERTY(EditAnywhere, Category = CameraControls)
	float MovementInterpSpeed = 2.0;

	FVector DesiredMovement;
#pragma endregion

#pragma region RotateValues
	UPROPERTY(EditAnywhere, Category = CameraControls)
	FRotator RotationAngle = FRotator(0.0f, 0.0f, 45.0f);

	UPROPERTY(EditAnywhere, Category = CameraControls)
	float RotateInterpSpeed = 2.0;

	FRotator DesiredRotation;
#pragma endregion

	//Code only fields
	AVictorPlayerController* VictorPlayerController;

	UEnhancedInputLocalPlayerSubsystem* Subsystem;

	//Functions
public:
	// Sets default values for this pawn's properties
	AVictorPlayerPawn();

private:
	// Input
	void ZoomInput(const FInputActionValue& Value);

	void BattleCamInput(const FInputActionValue& Value);

	void RotateInput(const FInputActionValue& Value);

	void PrimaryInput(const FInputActionValue& Value);

	void SecondaryInput(const FInputActionValue& Value);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	// Called to bind functionality to input
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

#pragma region Getters and Setters
	// Zoom Values
	float GetZoomMultiplier() const;
	void SetZoomMultiplier(float Value);

	float GetMinZoomValue() const;
	void SetMinZoomValue(float Value);

	float GetMaxZoomValue() const;
	void SetMaxZoomValue(float Value);

	float GetZoomInterpSpeed() const;
	void SetZoomInterpSpeed(float Value);

	// Movement Values
	float GetMovementMultiplier() const;
	void SetMovementMultiplier(float Value);

	float GetMovementInterpSpeed() const;
	void SetMovementInterpSpeed(float Value);

	// Rotate Values
	float GetRotationAngle() const;
	void SetRotationAngle(const float& Value);

	float GetRotateInterpSpeed() const;
	void SetRotateInterpSpeed(float Value);
#pragma endregion
};
