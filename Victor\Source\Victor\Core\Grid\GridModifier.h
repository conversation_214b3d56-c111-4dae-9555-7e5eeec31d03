// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Victor/Core/Grid/Utilities/TileTypeEnum.h"
#include "GridModifier.generated.h"

UCLASS()
class VICTOR_API AGridModifier : public AActor
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( VisibleAnywhere, Category = "Components" )
	USceneComponent* GridRootScene;

	UPROPERTY( VisibleAnywhere, Category = "Components" )
	UStaticMeshComponent* ModifierMesh;

	UPROPERTY( EditAnywhere, Category = Spawn )
	EGridShapeEnum Shape;

	UPROPERTY( EditAnywhere, Category = Spawn )
	ETileTypeEnum TileType;

	UPROPERTY( EditAnywhere, Category = Spawn )
	FLinearColor NoneColor = FLinearColor::Black;

	UPROPERTY( EditAnywhere, Category = Spawn )
	FLinearColor NormalColor = FLinearColor::White;

	UPROPERTY( EditAnywhere, Category = Spawn )
	FLinearColor ObstacleColor = FLinearColor::Red;

	UPROPERTY( EditDefaultsOnly, Category = "Shape Data" )
	TSoftObjectPtr<UDataTable> GridShapeDataTableSoftRef;

	//Functions
public:
	// Sets default values for this actor's properties
	AGridModifier();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick( float DeltaTime ) override;

	virtual void OnConstruction( const FTransform& Transform ) override;

	ETileTypeEnum GetTileType();
};
