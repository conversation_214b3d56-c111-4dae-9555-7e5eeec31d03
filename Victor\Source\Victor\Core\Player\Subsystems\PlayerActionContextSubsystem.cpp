// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayerActionContextSubsystem.h"
#include "Victor/Core/Player/Actions/PlayerAction.h"
#include "Victor/Core/Player/Actions/Create/AddTileAction.h"
#include "Victor/Core/Player/Actions/Create/RemoveTileAction.h"
#include "Victor/Core/Player/Actions/Default/DeselectTileAction.h"
#include "Victor/Core/Player/Actions/Default/SelectTileAction.h"
#include "Victor/Core/Player/Actions/Default/TileHoverAction.h"
#include "Victor/Core/Player/Actions/Pathfinding/ShowTileNeighborsHoverAction.h"

void UPlayerActionContextSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	SetupDefaultActions();
}


void UPlayerActionContextSubsystem::SetContext(EPlayerActionContext NewContext)
{
	if (CurrentContext != NewContext)
	{
		EPlayerActionContext OldContext = CurrentContext;
		CurrentContext = NewContext;

		UE_LOG(LogTemp, Log, TEXT("Player action context changed from %d to %d"), (int32)OldContext, (int32)NewContext);
	}
}


void UPlayerActionContextSubsystem::RegisterAction(EPlayerActionContext Context, const EPlayerInputAction InputAction, UObject* Action)
{
	if (!Action)
	{
		UE_LOG(LogTemp, Warning, TEXT("Attempted to register null action for input '%s' in context %d"), *UEnum::GetValueAsString(InputAction), (int32)Context);
		return;
	}

	// Verify the object implements IPlayerAction interface
	if (!Action->GetClass()->ImplementsInterface(UPlayerAction::StaticClass()))
	{
		UE_LOG(LogTemp, Warning, TEXT("Action object for input '%s' does not implement IPlayerAction interface"), *UEnum::GetValueAsString(InputAction));
		return;
	}

	ContextActions.FindOrAdd(Context).Actions.Add(InputAction, Action);
}

UObject* UPlayerActionContextSubsystem::GetActionForInput(const EPlayerInputAction InputAction) const
{
	if (const FActionMap* ActionMap = ContextActions.Find(CurrentContext))
	{
		return ActionMap->Actions.FindRef(InputAction);
	}

	return nullptr;
}


EPlayerActionContext UPlayerActionContextSubsystem::GetCurrentContext() const
{
	return CurrentContext;
}

void UPlayerActionContextSubsystem::SetupDefaultActions()
{
	// Default Actions
	USelectTileAction* SelectTileAction = NewObject<USelectTileAction>();
	UDeselectTileAction* DeselectTileAction = NewObject<UDeselectTileAction>();
	UTileHoverAction* TileHoverAction = NewObject<UTileHoverAction>();

	RegisterAction(EPlayerActionContext::Default, EPlayerInputAction::Primary, SelectTileAction);
	RegisterAction(EPlayerActionContext::Default, EPlayerInputAction::Secondary, DeselectTileAction);
	RegisterAction(EPlayerActionContext::Default, EPlayerInputAction::Continuous, TileHoverAction);

	// Create Actions
	//TODO Need to add modifications to height as well as hold buttons
	// This was covered in tutorial 21 but this seems super pointless thus skipping
	UAddTileAction* AddTileAction = NewObject<UAddTileAction>();
	URemoveTileAction* RemoveTileAction = NewObject<URemoveTileAction>();

	RegisterAction(EPlayerActionContext::Create, EPlayerInputAction::Primary, AddTileAction);
	RegisterAction(EPlayerActionContext::Create, EPlayerInputAction::Secondary, RemoveTileAction);

	//Pathfinding Actions
	UShowTileNeighborsHoverAction* ShowTileNeighborsHoverAction = NewObject<UShowTileNeighborsHoverAction>();

	RegisterAction(EPlayerActionContext::Pathfinding, EPlayerInputAction::Continuous, ShowTileNeighborsHoverAction);

	UE_LOG(LogTemp, Log, TEXT("Default player actions setup complete"));
}
