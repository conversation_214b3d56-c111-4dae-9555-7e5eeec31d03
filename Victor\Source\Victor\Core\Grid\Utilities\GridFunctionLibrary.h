// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "GridFunctionLibrary.generated.h"

enum class ETileTypeEnum : uint8;
/**
 * 
 */
UCLASS()
class VICTOR_API UGridFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static bool IsTileWalkable(ETileTypeEnum TileType);

	static FVector SnapVectorToVector(FVector Vector1, FVector Vector2);

	static bool IsFloatEven(float LocalFloat);

	static FText GetIndexText(FIntPoint Index);
};
