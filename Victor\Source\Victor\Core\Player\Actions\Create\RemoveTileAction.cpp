// Fill out your copyright notice in the Description page of Project Settings.


#include "RemoveTileAction.h"

#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"

void URemoveTileAction::Execute(UWorld* World)
{
	if (World)
	{
		const UGridWorldSubsystem* GridWorldSubsystem = World->GetSubsystem<UGridWorldSubsystem>();
		AGrid* CurrentGrid = GridWorldSubsystem->GetGrid();

		const FIntPoint Index = CurrentGrid->GetCursorTileIndex();
		CurrentGrid->RemoveGridTile(Index);
	}
}

bool URemoveTileAction::CanExecute(UWorld* World) const
{
	return true;
}

FString URemoveTileAction::GetActionName() const
{
	return TEXT("AddTileAction");
}
