// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "BoardLevelLoader.generated.h"

UCLASS(Abstract, Blueprintable)
class VICTOR_API UBoardLevelLoader : public UGameInstanceSubsystem
{
	GENERATED_BODY()

	//Fields
	int CurrentLevelIndex = -1;

	int PreviousLevelIndex = -1;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Levels")
	TArray<TSoftObjectPtr<UWorld>> BoardLevels;

	//Functions
public:
	// Begin USubsystem
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	// End USubsystem

private:
	void UnloadPrevLevel();

	void LoadCurrentLevel();

	UFUNCTION()
	static void OnLevelStreamed();

	UFUNCTION()
	void OnLevelUnload();

public:
	TArray<FString> GetGameLevelNamesForDropdown() const;

	void LoadLevelByIndex(int32 LevelIndex);
};
