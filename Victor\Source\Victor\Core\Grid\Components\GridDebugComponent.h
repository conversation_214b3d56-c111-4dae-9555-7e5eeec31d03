// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GridDebugComponent.generated.h"

struct FTileDataStruct;
class ATextRenderActor;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class VICTOR_API UGridDebugComponent : public UActorComponent
{
	GENERATED_BODY()

	UPROPERTY(Transient)
	TMap<FIntPoint, ATextRenderActor*> TileTextMap;

	UPROPERTY(Transient)
	bool bDebugEnabled = false;

public:
	// Sets default values for this component's properties
	UGridDebugComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	virtual void BeginDestroy() override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	ATextRenderActor* GetTileTextActor(FIntPoint Index);

	void DestroyTileTextActor(FIntPoint Index);

	void ClearTileTextActorMap();

	void UpdateTileText(FIntPoint Index, const FTileDataStruct* TileData);

	void SetDebugEnabled(bool bIsEnabled);

	bool GetDebugEnabled() const;
};
