// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Utilities/TileTypeEnum.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Grid.generated.h"

class UGridPathfindingComponent;
struct FGridShapeDataStruct;

class UGridDebugComponent;
class UGridVisualComponent;

USTRUCT(BlueprintType)
struct FTraceGroundResult
{
	GENERATED_BODY()

	UPROPERTY()
	FVector FoundLocation;
	UPROPERTY()
	bool HasHitSomething;
	UPROPERTY()
	ETileTypeEnum TileType;
};

UCLASS()
class VICTOR_API AGrid : public AActor
{
	GENERATED_BODY()

	//Fields
	//Components
	UPROPERTY(VisibleAnywhere, Category = "Components")
	USceneComponent* GridRootScene;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridVisualComponent* GridVisualComponent;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridDebugComponent* GridDebugComponent;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridPathfindingComponent* GridPathfindingComponent;

	//Blueprint Values
	UPROPERTY(EditAnywhere, Category = Spawn)
	FVector CenterLocation = FVector(0.0f, 0.0f, 0.0f);

	UPROPERTY(EditAnywhere, Category = Spawn)
	FVector TileSize = FVector(200.0f, 200.0f, 100.0f);

	UPROPERTY(EditAnywhere, Category = Spawn)
	FIntPoint TileCount = FIntPoint(10, 10);

	UPROPERTY(EditAnywhere, Category = Spawn)
	EGridShapeEnum Shape;

	//FDataTableRowHandle
	UPROPERTY(EditDefaultsOnly, meta = ( RowType = "GridShapeDataStruct" ), Category = Spawn)
	UDataTable* GridShapeDataTable;

	UPROPERTY(EditAnywhere, Category = Spawn)
	int8 GroundHeightOffset = 2;

	UPROPERTY(EditAnywhere, Category = Spawn)
	FCollisionResponseContainer GridCollisionResponse;

	UPROPERTY(EditAnywhere, Category = Spawn)
	TEnumAsByte<ECollisionEnabled::Type> GridCollisionEnabled;

	bool SpawningGrid = false;

	FVector BottomLeft = FVector(0.0f, 0.0f, 0.0f);

	TMap<FIntPoint, FTileDataStruct> TileDataMap;
	//Functions
public:
	// Sets default values for this actor's properties
	AGrid();

private:
	const FGridShapeDataStruct* SetStaticMeshValues();

	FVector2D GetAlignedTileCount() const;

	FVector FindGridBottomLeftLocation();

	static FRotator GetTriangleRotation(int XIndex, int YIndex);

	void SpawnGenericTiles(const FGridShapeDataStruct* GridShapeData, bool UseEnvironment);

	void SpawnHexTiles(const FGridShapeDataStruct* GridShapeData, bool UseEnvironment);

	FIntPoint GetSquareTileIndex(const FVector& GridLocation) const;

	FIntPoint GetTriangleTileIndex(const FVector& GridLocation) const;

	FIntPoint GetHexagonTileIndex(const FVector& GridLocation) const;

	FIntPoint GetTileIndex(const FVector& WorldLocation) const;

	//Util Functions

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	virtual void OnConstruction(const FTransform& Transform) override;

	FTraceGroundResult TraceBoardGround(const FVector& Location) const;

	FVector GetTileLocation(int XIndex, int YIndex) const;

	FRotator GetTileRotation(int XIndex, int YIndex) const;

	FVector GetTileScale();

	void AddGridTile(const FTileDataStruct& TileData);

	void RemoveGridTile(FIntPoint Index);

	void SpawnGrid(bool UseEnvironment);

	bool IsSpawningGrid() const;

	void SetIsSpawningGridForDelay();

	FVector GetGridCursorLocation() const;

	FIntPoint GetCursorTileIndex() const;

	void AddTileState(FIntPoint Index, ETileStateEnum State);

	void RemoveTileState(FIntPoint Index, ETileStateEnum State);

	TArray<FIntPoint> GetAllTilesWithState(ETileStateEnum State) const;

	void ClearStateFromTiles(ETileStateEnum State);

	bool IsIndexValid(FIntPoint Index) const;

#pragma region Debug Information
	void UpdateDebugTileInfo(FIntPoint Index);
#pragma endregion

#pragma region Getters and Setters
	// CenterLocation
	const FGridShapeDataStruct* GetGridShapeData();

	FVector GetCenterLocation() const;
	void SetCenterLocation(const FVector& NewCenterLocation);

	//BottomLeft
	FVector GetBottomLeft() const;

	// TileSize
	FVector GetTileSize() const;
	void SetTileSize(const FVector& NewTileSize);

	// TileCount
	FIntPoint GetTileCount() const;
	void SetTileCount(const FIntPoint& NewTileCount);

	// Shape
	EGridShapeEnum GetShape() const;
	void SetShape(const EGridShapeEnum& NewShape);

	// Tiles
	TMap<FIntPoint, FTileDataStruct> GetTileDataMap();

	void SetTileTextDebug(bool bIsTileTextDebugMode);

	bool GetTileTextDebug() const;

	TArray<FIntPoint> GetValidTileNeighbors(FIntPoint Index) const;
#pragma endregion
};
