// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "DebugSliderActivatableWidget.generated.h"

class UCommonTextBlock;
class USpinBox;

DECLARE_DELEGATE_OneParam( FSliderValueChangeDelegate, float );

/**
 *
 */
UCLASS()
class VICTOR_API UDebugSliderActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FText SliderName;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxSliderValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxValues;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	float Value;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	FVector2D MinMaxFractDigits;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	bool AlwaysSnapToDelta;

	UPROPERTY( EditAnywhere, Category = "SliderValues" )
	float Delta;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UCommonTextBlock* DisplayName;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	USpinBox* DebugSpinBox;

public:
	FSliderValueChangeDelegate ValueChangeDelegate;

	//Functions
private:
	UFUNCTION()
	void OnSliderValueChange( float NewValue );

public:
	void NativePreConstruct() override;

	void NativeOnInitialized() override;

	float GetValue() const;
	void SetValue( float NewValue );

};
