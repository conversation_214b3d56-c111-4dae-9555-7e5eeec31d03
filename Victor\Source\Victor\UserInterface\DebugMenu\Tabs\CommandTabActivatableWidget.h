// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "CommandTabActivatableWidget.generated.h"

class UConsoleCommandButtonBase;

/**
 *
 */
UCLASS()
class VICTOR_API UCommandTabActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* StatFpsButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* StatUnitButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* StatUObjectButton;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* ShowFlagStaticMesh0Button;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* ShowFlagStaticMesh1Button;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* ShowFlagWireframe0Button;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UConsoleCommandButtonBase* ShowFlagWireframe1Button;

public:
	void NativeOnInitialized() override;
};
